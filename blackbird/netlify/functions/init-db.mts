import type { Context, Config } from "@netlify/functions";
import { initializeDatabase, createIndexes } from "../../src/lib/database";

export default async (req: Request, context: Context) => {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Method not allowed' 
      }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Initialize database schema
    await initializeDatabase();
    
    // Create performance indexes
    await createIndexes();

    return new Response(JSON.stringify({
      success: true,
      message: 'Database initialized successfully'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Database initialization error:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to initialize database',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const config: Config = {
  path: "/api/init-db"
};
