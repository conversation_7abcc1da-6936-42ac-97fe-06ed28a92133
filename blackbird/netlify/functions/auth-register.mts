import type { Context, Config } from "@netlify/functions";
import { createUser, validateRegistrationData } from "../../src/lib/auth";
import { CreateUserRequest, ApiResponse } from "../../src/types/game";

export default async (req: Request, context: Context) => {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Method not allowed' 
      }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Parse request body
    const body = await req.json() as CreateUserRequest;
    
    // Validate input data
    const validation = validateRegistrationData(body);
    if (!validation.valid) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Validation failed',
        details: validation.errors
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Create user
    const user = await createUser(body);

    const response: ApiResponse = {
      success: true,
      data: user,
      message: 'User created successfully'
    };

    return new Response(JSON.stringify(response), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Registration error:', error);
    
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Registration failed'
    };

    return new Response(JSON.stringify(response), {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const config: Config = {
  path: "/api/auth/register"
};
