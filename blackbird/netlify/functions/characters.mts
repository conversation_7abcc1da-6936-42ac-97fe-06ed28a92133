import type { Context, Config } from "@netlify/functions";
import { authenticateRequest } from "../../src/lib/auth";
import { 
  createCharacter, 
  getUser<PERSON>haracters, 
  getCharacterById,
  getCharacterInventory,
  toggleItemEquipped,
  useConsumableItem,
  getCharacterEffectiveStats
} from "../../src/lib/characters";
import { CreateCharacterRequest, ApiResponse } from "../../src/types/game";

export default async (req: Request, context: Context) => {
  try {
    // Authenticate user
    const user = await authenticateRequest(req);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Authentication required'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const url = new URL(req.url);
    const pathParts = url.pathname.split('/').filter(Boolean);
    const characterId = pathParts[2]; // /api/characters/{id}
    const action = pathParts[3]; // /api/characters/{id}/{action}

    switch (req.method) {
      case 'GET':
        if (!characterId) {
          // Get all user characters
          const characters = await getUserCharacters(user.id);
          return new Response(JSON.stringify({
            success: true,
            data: characters
          }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          });
        } else if (action === 'inventory') {
          // Get character inventory
          const character = await getCharacterById(characterId, user.id);
          if (!character) {
            return new Response(JSON.stringify({
              success: false,
              error: 'Character not found'
            }), {
              status: 404,
              headers: { 'Content-Type': 'application/json' }
            });
          }

          const inventory = await getCharacterInventory(characterId);
          return new Response(JSON.stringify({
            success: true,
            data: inventory
          }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          });
        } else if (action === 'stats') {
          // Get character with effective stats
          const character = await getCharacterById(characterId, user.id);
          if (!character) {
            return new Response(JSON.stringify({
              success: false,
              error: 'Character not found'
            }), {
              status: 404,
              headers: { 'Content-Type': 'application/json' }
            });
          }

          const characterWithStats = await getCharacterEffectiveStats(characterId);
          return new Response(JSON.stringify({
            success: true,
            data: characterWithStats
          }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          });
        } else {
          // Get specific character
          const character = await getCharacterById(characterId, user.id);
          if (!character) {
            return new Response(JSON.stringify({
              success: false,
              error: 'Character not found'
            }), {
              status: 404,
              headers: { 'Content-Type': 'application/json' }
            });
          }

          return new Response(JSON.stringify({
            success: true,
            data: character
          }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          });
        }

      case 'POST':
        if (!characterId) {
          // Create new character
          const body = await req.json() as CreateCharacterRequest;
          
          if (!body.name || body.name.trim().length === 0) {
            return new Response(JSON.stringify({
              success: false,
              error: 'Character name is required'
            }), {
              status: 400,
              headers: { 'Content-Type': 'application/json' }
            });
          }

          const character = await createCharacter(user.id, body);
          return new Response(JSON.stringify({
            success: true,
            data: character,
            message: 'Character created successfully'
          }), {
            status: 201,
            headers: { 'Content-Type': 'application/json' }
          });
        } else if (action === 'equip') {
          // Toggle item equipped status
          const body = await req.json() as { inventory_item_id: string };
          
          const character = await getCharacterById(characterId, user.id);
          if (!character) {
            return new Response(JSON.stringify({
              success: false,
              error: 'Character not found'
            }), {
              status: 404,
              headers: { 'Content-Type': 'application/json' }
            });
          }

          const updatedItem = await toggleItemEquipped(characterId, body.inventory_item_id);
          return new Response(JSON.stringify({
            success: true,
            data: updatedItem,
            message: 'Item equipped status updated'
          }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          });
        } else if (action === 'use-item') {
          // Use consumable item
          const body = await req.json() as { inventory_item_id: string };
          
          const character = await getCharacterById(characterId, user.id);
          if (!character) {
            return new Response(JSON.stringify({
              success: false,
              error: 'Character not found'
            }), {
              status: 404,
              headers: { 'Content-Type': 'application/json' }
            });
          }

          const result = await useConsumableItem(characterId, body.inventory_item_id);
          return new Response(JSON.stringify({
            success: true,
            data: result,
            message: 'Item used successfully'
          }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          });
        }
        break;

      default:
        return new Response(JSON.stringify({
          success: false,
          error: 'Method not allowed'
        }), {
          status: 405,
          headers: { 'Content-Type': 'application/json' }
        });
    }

    return new Response(JSON.stringify({
      success: false,
      error: 'Invalid request'
    }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Characters API error:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const config: Config = {
  path: "/api/characters/*"
};
