import type { Context, Config } from "@netlify/functions";
import { authenticateRequest } from "../../src/lib/auth";
import { 
  createCombatSession, 
  getCombatSession, 
  processCombatAction,
  getCombatHistory
} from "../../src/lib/combat";
import { getCharacterById } from "../../src/lib/characters";
import { CombatActionRequest, ApiResponse } from "../../src/types/game";

export default async (req: Request, context: Context) => {
  try {
    // Authenticate user
    const user = await authenticateRequest(req);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Authentication required'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const url = new URL(req.url);
    const pathParts = url.pathname.split('/').filter(Boolean);
    const combatId = pathParts[2]; // /api/combat/{id}
    const action = pathParts[3]; // /api/combat/{id}/{action}

    switch (req.method) {
      case 'GET':
        if (!combatId) {
          // Get combat history
          const characterId = url.searchParams.get('character_id');
          if (!characterId) {
            return new Response(JSON.stringify({
              success: false,
              error: 'Character ID required'
            }), {
              status: 400,
              headers: { 'Content-Type': 'application/json' }
            });
          }

          // Verify character belongs to user
          const character = await getCharacterById(characterId, user.id);
          if (!character) {
            return new Response(JSON.stringify({
              success: false,
              error: 'Character not found'
            }), {
              status: 404,
              headers: { 'Content-Type': 'application/json' }
            });
          }

          const limit = parseInt(url.searchParams.get('limit') || '10');
          const history = await getCombatHistory(characterId, limit);

          return new Response(JSON.stringify({
            success: true,
            data: history
          }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          });
        } else {
          // Get specific combat session
          const combat = await getCombatSession(combatId);
          if (!combat) {
            return new Response(JSON.stringify({
              success: false,
              error: 'Combat session not found'
            }), {
              status: 404,
              headers: { 'Content-Type': 'application/json' }
            });
          }

          // Verify combat belongs to user's character
          const character = await getCharacterById(combat.character_id, user.id);
          if (!character) {
            return new Response(JSON.stringify({
              success: false,
              error: 'Access denied'
            }), {
              status: 403,
              headers: { 'Content-Type': 'application/json' }
            });
          }

          return new Response(JSON.stringify({
            success: true,
            data: combat
          }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          });
        }

      case 'POST':
        if (!combatId) {
          // Start new combat
          const body = await req.json() as { 
            game_session_id: string; 
            character_id: string; 
            enemy_type: string; 
          };

          if (!body.game_session_id || !body.character_id || !body.enemy_type) {
            return new Response(JSON.stringify({
              success: false,
              error: 'Game session ID, character ID, and enemy type are required'
            }), {
              status: 400,
              headers: { 'Content-Type': 'application/json' }
            });
          }

          // Verify character belongs to user
          const character = await getCharacterById(body.character_id, user.id);
          if (!character) {
            return new Response(JSON.stringify({
              success: false,
              error: 'Character not found'
            }), {
              status: 404,
              headers: { 'Content-Type': 'application/json' }
            });
          }

          const combat = await createCombatSession(
            body.game_session_id,
            body.character_id,
            body.enemy_type
          );

          return new Response(JSON.stringify({
            success: true,
            data: combat,
            message: 'Combat started'
          }), {
            status: 201,
            headers: { 'Content-Type': 'application/json' }
          });
        } else if (action === 'action') {
          // Perform combat action
          const combat = await getCombatSession(combatId);
          if (!combat) {
            return new Response(JSON.stringify({
              success: false,
              error: 'Combat session not found'
            }), {
              status: 404,
              headers: { 'Content-Type': 'application/json' }
            });
          }

          // Verify combat belongs to user's character
          const character = await getCharacterById(combat.character_id, user.id);
          if (!character) {
            return new Response(JSON.stringify({
              success: false,
              error: 'Access denied'
            }), {
              status: 403,
              headers: { 'Content-Type': 'application/json' }
            });
          }

          const actionData = await req.json() as CombatActionRequest;

          if (!actionData.action_type) {
            return new Response(JSON.stringify({
              success: false,
              error: 'Action type is required'
            }), {
              status: 400,
              headers: { 'Content-Type': 'application/json' }
            });
          }

          const result = await processCombatAction(combatId, actionData);

          return new Response(JSON.stringify({
            success: true,
            data: result,
            message: 'Action processed'
          }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          });
        }
        break;

      default:
        return new Response(JSON.stringify({
          success: false,
          error: 'Method not allowed'
        }), {
          status: 405,
          headers: { 'Content-Type': 'application/json' }
        });
    }

    return new Response(JSON.stringify({
      success: false,
      error: 'Invalid request'
    }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Combat API error:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const config: Config = {
  path: "/api/combat/*"
};
