import type { Context, Config } from "@netlify/functions";
import { authenticateUser } from "../../src/lib/auth";
import { LoginRequest, ApiResponse } from "../../src/types/game";

export default async (req: Request, context: Context) => {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Method not allowed' 
      }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Parse request body
    const body = await req.json() as LoginRequest;
    
    // Validate required fields
    if (!body.email || !body.password) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Email and password are required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Authenticate user
    const { user, token } = await authenticateUser(body);

    const response: ApiResponse = {
      success: true,
      data: { user, token },
      message: 'Login successful'
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Login error:', error);
    
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Login failed'
    };

    return new Response(JSON.stringify(response), {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const config: Config = {
  path: "/api/auth/login"
};
