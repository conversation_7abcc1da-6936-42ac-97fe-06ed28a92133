import type { Context, Config } from "@netlify/functions";
import { seedAll } from "../../src/lib/seed-data";

export default async (req: Request, context: Context) => {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Method not allowed' 
      }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Seed the database with initial data
    await seedAll();

    return new Response(JSON.stringify({
      success: true,
      message: 'Database seeded successfully'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Database seeding error:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to seed database',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const config: Config = {
  path: "/api/seed-data"
};
