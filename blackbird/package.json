{"name": "blackbird", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@netlify/functions": "^4.1.10", "@netlify/neon": "^0.1.0", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "uuid": "^11.1.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}