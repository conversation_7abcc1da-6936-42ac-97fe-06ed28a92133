(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/store/gameStore.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useAuth": (()=>useAuth),
    "useCharacter": (()=>useCharacter),
    "useCombat": (()=>useCombat),
    "useGameStore": (()=>useGameStore),
    "useUI": (()=>useUI)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature();
;
;
const useGameStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["devtools"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        // Initial state
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        characters: [],
        currentCharacter: null,
        characterInventory: [],
        currentSession: null,
        currentDungeon: null,
        currentFloor: null,
        gameState: null,
        currentCombat: null,
        isInCombat: false,
        lastAction: null,
        currentView: 'menu',
        showInventory: false,
        notifications: [],
        // Auth actions
        setAuth: (user, token)=>set({
                user,
                token,
                isAuthenticated: true,
                isLoading: false
            }),
        logout: ()=>set({
                user: null,
                token: null,
                isAuthenticated: false,
                currentCharacter: null,
                characters: [],
                characterInventory: [],
                currentSession: null,
                currentDungeon: null,
                currentFloor: null,
                gameState: null,
                currentCombat: null,
                isInCombat: false,
                currentView: 'menu'
            }),
        setAuthLoading: (loading)=>set({
                isLoading: loading
            }),
        // Character actions
        setCharacters: (characters)=>set({
                characters
            }),
        setCurrentCharacter: (character)=>set({
                currentCharacter: character,
                currentView: character ? 'dungeon' : 'character-select'
            }),
        updateCharacter: (character)=>set((state)=>({
                    currentCharacter: state.currentCharacter?.id === character.id ? character : state.currentCharacter,
                    characters: state.characters.map((c)=>c.id === character.id ? character : c)
                })),
        setCharacterInventory: (inventory)=>set({
                characterInventory: inventory
            }),
        updateInventoryItem: (item)=>set((state)=>({
                    characterInventory: state.characterInventory.map((i)=>i.id === item.id ? item : i)
                })),
        removeInventoryItem: (itemId)=>set((state)=>({
                    characterInventory: state.characterInventory.filter((i)=>i.id !== itemId)
                })),
        setCharacterLoading: (loading)=>set({
                isLoading: loading
            }),
        // Game session actions
        setCurrentSession: (session)=>set({
                currentSession: session
            }),
        setCurrentDungeon: (dungeon)=>set({
                currentDungeon: dungeon
            }),
        setCurrentFloor: (floor)=>set({
                currentFloor: floor
            }),
        setGameState: (state)=>set({
                gameState: state
            }),
        setGameLoading: (loading)=>set({
                isLoading: loading
            }),
        // Combat actions
        setCurrentCombat: (combat)=>set({
                currentCombat: combat,
                isInCombat: !!combat,
                currentView: combat ? 'combat' : 'dungeon'
            }),
        setInCombat: (inCombat)=>set({
                isInCombat: inCombat
            }),
        setCombatLoading: (loading)=>set({
                isLoading: loading
            }),
        setLastAction: (action)=>set({
                lastAction: action
            }),
        // UI actions
        setCurrentView: (view)=>set({
                currentView: view
            }),
        setShowInventory: (show)=>set({
                showInventory: show
            }),
        addNotification: (notification)=>set((state)=>({
                    notifications: [
                        ...state.notifications,
                        {
                            ...notification,
                            id: Date.now().toString(),
                            timestamp: Date.now()
                        }
                    ]
                })),
        removeNotification: (id)=>set((state)=>({
                    notifications: state.notifications.filter((n)=>n.id !== id)
                })),
        clearNotifications: ()=>set({
                notifications: []
            }),
        // Optimistic updates
        optimisticCharacterUpdate: (updates)=>set((state)=>{
                if (!state.currentCharacter) return state;
                const updatedCharacter = {
                    ...state.currentCharacter,
                    ...updates
                };
                return {
                    currentCharacter: updatedCharacter,
                    characters: state.characters.map((c)=>c.id === updatedCharacter.id ? updatedCharacter : c)
                };
            }),
        revertOptimisticUpdate: ()=>{
        // This would revert to the last known server state
        // Implementation depends on how you want to handle rollbacks
        },
        // State synchronization
        syncWithServer: async ()=>{
            const state = get();
            if (!state.isAuthenticated || !state.token) return;
            try {
                // Sync character data
                if (state.currentCharacter) {
                    const response = await fetch(`/api/characters/${state.currentCharacter.id}`, {
                        headers: {
                            'Authorization': `Bearer ${state.token}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    if (response.ok) {
                        const { data: character } = await response.json();
                        set({
                            currentCharacter: character
                        });
                    }
                }
                // Sync game session if active
                if (state.currentSession) {
                // Implement session sync logic
                }
                // Sync combat if active
                if (state.currentCombat) {
                // Implement combat sync logic
                }
            } catch (error) {
                console.error('Failed to sync with server:', error);
            }
        }
    }), {
    name: 'blackbird-game-store',
    partialize: (state)=>({
            user: state.user,
            token: state.token,
            isAuthenticated: state.isAuthenticated,
            currentCharacter: state.currentCharacter,
            currentView: state.currentView
        })
}), {
    name: 'GameStore'
}));
const useAuth = ()=>{
    _s();
    return useGameStore({
        "useAuth.useGameStore": (state)=>({
                user: state.user,
                token: state.token,
                isAuthenticated: state.isAuthenticated,
                isLoading: state.isLoading,
                setAuth: state.setAuth,
                logout: state.logout,
                setAuthLoading: state.setAuthLoading
            })
    }["useAuth.useGameStore"]);
};
_s(useAuth, "m++mmMMqXYxpz65W2LrR4Awzsog=", false, function() {
    return [
        useGameStore
    ];
});
const useCharacter = ()=>{
    _s1();
    return useGameStore({
        "useCharacter.useGameStore": (state)=>({
                characters: state.characters,
                currentCharacter: state.currentCharacter,
                characterInventory: state.characterInventory,
                isLoading: state.isLoading,
                setCharacters: state.setCharacters,
                setCurrentCharacter: state.setCurrentCharacter,
                updateCharacter: state.updateCharacter,
                setCharacterInventory: state.setCharacterInventory,
                updateInventoryItem: state.updateInventoryItem,
                removeInventoryItem: state.removeInventoryItem
            })
    }["useCharacter.useGameStore"]);
};
_s1(useCharacter, "m++mmMMqXYxpz65W2LrR4Awzsog=", false, function() {
    return [
        useGameStore
    ];
});
const useCombat = ()=>{
    _s2();
    return useGameStore({
        "useCombat.useGameStore": (state)=>({
                currentCombat: state.currentCombat,
                isInCombat: state.isInCombat,
                isLoading: state.isLoading,
                lastAction: state.lastAction,
                setCurrentCombat: state.setCurrentCombat,
                setInCombat: state.setInCombat,
                setCombatLoading: state.setCombatLoading,
                setLastAction: state.setLastAction
            })
    }["useCombat.useGameStore"]);
};
_s2(useCombat, "m++mmMMqXYxpz65W2LrR4Awzsog=", false, function() {
    return [
        useGameStore
    ];
});
const useUI = ()=>{
    _s3();
    return useGameStore({
        "useUI.useGameStore": (state)=>({
                currentView: state.currentView,
                showInventory: state.showInventory,
                notifications: state.notifications,
                setCurrentView: state.setCurrentView,
                setShowInventory: state.setShowInventory,
                addNotification: state.addNotification,
                removeNotification: state.removeNotification,
                clearNotifications: state.clearNotifications
            })
    }["useUI.useGameStore"]);
};
_s3(useUI, "m++mmMMqXYxpz65W2LrR4Awzsog=", false, function() {
    return [
        useGameStore
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "apiCache": (()=>apiCache),
    "apiClient": (()=>apiClient),
    "cachedRequest": (()=>cachedRequest),
    "debounce": (()=>debounce),
    "useApi": (()=>useApi),
    "withOptimisticUpdate": (()=>withOptimisticUpdate),
    "withRetry": (()=>withRetry)
});
class ApiClient {
    baseUrl;
    token = null;
    constructor(baseUrl = '/api'){
        this.baseUrl = baseUrl;
    }
    setToken(token) {
        this.token = token;
    }
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }
        return headers;
    }
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            ...options,
            headers: {
                ...this.getHeaders(),
                ...options.headers
            }
        };
        try {
            const response = await fetch(url, config);
            const data = await response.json();
            if (!response.ok) {
                throw new Error(data.error || `HTTP error! status: ${response.status}`);
            }
            return data;
        } catch (error) {
            console.error(`API request failed: ${endpoint}`, error);
            throw error;
        }
    }
    // Auth endpoints
    async register(email, username, password) {
        return this.request('/auth/register', {
            method: 'POST',
            body: JSON.stringify({
                email,
                username,
                password
            })
        });
    }
    async login(email, password) {
        return this.request('/auth/login', {
            method: 'POST',
            body: JSON.stringify({
                email,
                password
            })
        });
    }
    // Character endpoints
    async getCharacters() {
        return this.request('/characters');
    }
    async getCharacter(characterId) {
        return this.request(`/characters/${characterId}`);
    }
    async createCharacter(name) {
        return this.request('/characters', {
            method: 'POST',
            body: JSON.stringify({
                name
            })
        });
    }
    async getCharacterInventory(characterId) {
        return this.request(`/characters/${characterId}/inventory`);
    }
    async getCharacterStats(characterId) {
        return this.request(`/characters/${characterId}/stats`);
    }
    async equipItem(characterId, inventoryItemId) {
        return this.request(`/characters/${characterId}/equip`, {
            method: 'POST',
            body: JSON.stringify({
                inventory_item_id: inventoryItemId
            })
        });
    }
    async useItem(characterId, inventoryItemId) {
        return this.request(`/characters/${characterId}/use-item`, {
            method: 'POST',
            body: JSON.stringify({
                inventory_item_id: inventoryItemId
            })
        });
    }
    // Game session endpoints
    async startGameSession(characterId, dungeonId) {
        return this.request('/game/start', {
            method: 'POST',
            body: JSON.stringify({
                character_id: characterId,
                dungeon_id: dungeonId
            })
        });
    }
    async getGameSession(sessionId) {
        return this.request(`/game/session/${sessionId}`);
    }
    async moveToRoom(sessionId, roomId) {
        return this.request(`/game/session/${sessionId}/move`, {
            method: 'POST',
            body: JSON.stringify({
                room_id: roomId
            })
        });
    }
    async endGameSession(sessionId) {
        return this.request(`/game/session/${sessionId}/end`, {
            method: 'POST'
        });
    }
    // Combat endpoints
    async startCombat(sessionId, enemyId) {
        return this.request('/combat/start', {
            method: 'POST',
            body: JSON.stringify({
                session_id: sessionId,
                enemy_id: enemyId
            })
        });
    }
    async getCombatSession(combatId) {
        return this.request(`/combat/${combatId}`);
    }
    async performCombatAction(combatId, action) {
        return this.request(`/combat/${combatId}/action`, {
            method: 'POST',
            body: JSON.stringify(action)
        });
    }
    // Dungeon endpoints
    async getDungeons() {
        return this.request('/dungeons');
    }
    async getDungeon(dungeonId) {
        return this.request(`/dungeons/${dungeonId}`);
    }
    // Items endpoints
    async getItems() {
        return this.request('/items');
    }
    async getItem(itemId) {
        return this.request(`/items/${itemId}`);
    }
    // Admin endpoints
    async initializeDatabase() {
        return this.request('/init-db', {
            method: 'POST'
        });
    }
    async seedDatabase() {
        return this.request('/seed-data', {
            method: 'POST'
        });
    }
}
const apiClient = new ApiClient();
function useApi() {
    return apiClient;
}
async function withOptimisticUpdate(optimisticUpdate, serverUpdate, revertUpdate) {
    // Apply optimistic update immediately
    optimisticUpdate();
    try {
        // Perform server update
        const result = await serverUpdate();
        return result;
    } catch (error) {
        // Revert optimistic update on failure
        revertUpdate();
        throw error;
    }
}
async function withRetry(operation, maxRetries = 3, delay = 1000) {
    let lastError;
    for(let attempt = 1; attempt <= maxRetries; attempt++){
        try {
            return await operation();
        } catch (error) {
            lastError = error;
            if (attempt === maxRetries) {
                throw lastError;
            }
            // Wait before retrying
            await new Promise((resolve)=>setTimeout(resolve, delay * attempt));
        }
    }
    throw lastError;
}
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
// Cache for API responses
class ApiCache {
    cache = new Map();
    set(key, data, ttl = 5 * 60 * 1000) {
        this.cache.set(key, {
            data,
            timestamp: Date.now(),
            ttl
        });
    }
    get(key) {
        const entry = this.cache.get(key);
        if (!entry) {
            return null;
        }
        if (Date.now() - entry.timestamp > entry.ttl) {
            this.cache.delete(key);
            return null;
        }
        return entry.data;
    }
    clear() {
        this.cache.clear();
    }
    delete(key) {
        this.cache.delete(key);
    }
}
const apiCache = new ApiCache();
async function cachedRequest(key, requestFn, ttl) {
    // Check cache first
    const cached = apiCache.get(key);
    if (cached) {
        return cached;
    }
    // Make request and cache result
    const result = await requestFn();
    apiCache.set(key, result, ttl);
    return result;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useGameActions.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useAuthActions": (()=>useAuthActions),
    "useCharacterActions": (()=>useCharacterActions),
    "useCombatActions": (()=>useCombatActions),
    "useGameSessionActions": (()=>useGameSessionActions),
    "useNotifications": (()=>useNotifications)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/gameStore.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature();
;
;
;
function useAuthActions() {
    _s();
    const { setAuth, logout, setAuthLoading, addNotification } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGameStore"])();
    const login = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthActions.useCallback[login]": async (credentials)=>{
            setAuthLoading(true);
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].login(credentials.email, credentials.password);
                if (response.success && response.data) {
                    const { user, token } = response.data;
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].setToken(token);
                    setAuth(user, token);
                    addNotification({
                        type: 'success',
                        message: 'Login successful!'
                    });
                    return {
                        success: true
                    };
                } else {
                    throw new Error(response.error || 'Login failed');
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : 'Login failed';
                addNotification({
                    type: 'error',
                    message
                });
                return {
                    success: false,
                    error: message
                };
            } finally{
                setAuthLoading(false);
            }
        }
    }["useAuthActions.useCallback[login]"], [
        setAuth,
        setAuthLoading,
        addNotification
    ]);
    const register = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthActions.useCallback[register]": async (userData)=>{
            setAuthLoading(true);
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].register(userData.email, userData.username, userData.password);
                if (response.success) {
                    addNotification({
                        type: 'success',
                        message: 'Registration successful! Please log in.'
                    });
                    return {
                        success: true
                    };
                } else {
                    throw new Error(response.error || 'Registration failed');
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : 'Registration failed';
                addNotification({
                    type: 'error',
                    message
                });
                return {
                    success: false,
                    error: message
                };
            } finally{
                setAuthLoading(false);
            }
        }
    }["useAuthActions.useCallback[register]"], [
        setAuthLoading,
        addNotification
    ]);
    const handleLogout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthActions.useCallback[handleLogout]": ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].setToken(null);
            logout();
            addNotification({
                type: 'info',
                message: 'Logged out successfully'
            });
        }
    }["useAuthActions.useCallback[handleLogout]"], [
        logout,
        addNotification
    ]);
    return {
        login,
        register,
        logout: handleLogout
    };
}
_s(useAuthActions, "+MgzTAEdP6BZpMFlKZ4iBo8lb6I=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGameStore"]
    ];
});
function useCharacterActions() {
    _s1();
    const { setCharacters, setCurrentCharacter, updateCharacter, setCharacterInventory, updateInventoryItem, removeInventoryItem, optimisticCharacterUpdate, revertOptimisticUpdate, addNotification } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGameStore"])();
    const loadCharacters = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCharacterActions.useCallback[loadCharacters]": async ()=>{
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].getCharacters();
                if (response.success && response.data) {
                    setCharacters(response.data);
                }
            } catch (error) {
                addNotification({
                    type: 'error',
                    message: 'Failed to load characters'
                });
            }
        }
    }["useCharacterActions.useCallback[loadCharacters]"], [
        setCharacters,
        addNotification
    ]);
    const createCharacter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCharacterActions.useCallback[createCharacter]": async (characterData)=>{
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].createCharacter(characterData.name);
                if (response.success && response.data) {
                    const newCharacter = response.data;
                    setCharacters(await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].getCharacters().then({
                        "useCharacterActions.useCallback[createCharacter]": (r)=>r.data || []
                    }["useCharacterActions.useCallback[createCharacter]"]));
                    addNotification({
                        type: 'success',
                        message: `Character "${newCharacter.name}" created successfully!`
                    });
                    return {
                        success: true,
                        character: newCharacter
                    };
                } else {
                    throw new Error(response.error || 'Failed to create character');
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : 'Failed to create character';
                addNotification({
                    type: 'error',
                    message
                });
                return {
                    success: false,
                    error: message
                };
            }
        }
    }["useCharacterActions.useCallback[createCharacter]"], [
        setCharacters,
        addNotification
    ]);
    const selectCharacter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCharacterActions.useCallback[selectCharacter]": async (character)=>{
            setCurrentCharacter(character);
            // Load character inventory
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].getCharacterInventory(character.id);
                if (response.success && response.data) {
                    setCharacterInventory(response.data);
                }
            } catch (error) {
                addNotification({
                    type: 'error',
                    message: 'Failed to load character inventory'
                });
            }
        }
    }["useCharacterActions.useCallback[selectCharacter]"], [
        setCurrentCharacter,
        setCharacterInventory,
        addNotification
    ]);
    const useItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCharacterActions.useCallback[useItem]": async (characterId, inventoryItemId)=>{
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].useItem(characterId, inventoryItemId);
                if (response.success && response.data) {
                    const { character, itemUsed } = response.data;
                    updateCharacter(character);
                    if (itemUsed) {
                        // Refresh inventory
                        const inventoryResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].getCharacterInventory(characterId);
                        if (inventoryResponse.success && inventoryResponse.data) {
                            setCharacterInventory(inventoryResponse.data);
                        }
                    }
                    addNotification({
                        type: 'success',
                        message: 'Item used successfully!'
                    });
                    return {
                        success: true
                    };
                } else {
                    throw new Error(response.error || 'Failed to use item');
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : 'Failed to use item';
                addNotification({
                    type: 'error',
                    message
                });
                return {
                    success: false,
                    error: message
                };
            }
        }
    }["useCharacterActions.useCallback[useItem]"], [
        updateCharacter,
        setCharacterInventory,
        addNotification
    ]);
    const equipItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCharacterActions.useCallback[equipItem]": async (characterId, inventoryItemId)=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["withOptimisticUpdate"])({
                "useCharacterActions.useCallback[equipItem]": // Optimistic update
                ()=>{
                // This would update the UI immediately
                // Implementation depends on your specific UI needs
                }
            }["useCharacterActions.useCallback[equipItem]"], {
                "useCharacterActions.useCallback[equipItem]": // Server update
                async ()=>{
                    const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].equipItem(characterId, inventoryItemId);
                    if (response.success && response.data) {
                        updateInventoryItem(response.data);
                        addNotification({
                            type: 'success',
                            message: 'Item equipped successfully!'
                        });
                        return response.data;
                    } else {
                        throw new Error(response.error || 'Failed to equip item');
                    }
                }
            }["useCharacterActions.useCallback[equipItem]"], {
                "useCharacterActions.useCallback[equipItem]": // Revert function
                ()=>{
                    revertOptimisticUpdate();
                }
            }["useCharacterActions.useCallback[equipItem]"]);
        }
    }["useCharacterActions.useCallback[equipItem]"], [
        updateInventoryItem,
        revertOptimisticUpdate,
        addNotification
    ]);
    return {
        loadCharacters,
        createCharacter,
        selectCharacter,
        useItem,
        equipItem
    };
}
_s1(useCharacterActions, "zjOLVFyefc86Kd7PV4h+DJ3A4Q4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGameStore"]
    ];
});
function useGameSessionActions() {
    _s2();
    const { setCurrentSession, setCurrentDungeon, setCurrentFloor, setGameState, addNotification } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGameStore"])();
    const startGameSession = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useGameSessionActions.useCallback[startGameSession]": async (characterId, dungeonId)=>{
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].startGameSession(characterId, dungeonId);
                if (response.success && response.data) {
                    const session = response.data;
                    setCurrentSession(session);
                    addNotification({
                        type: 'success',
                        message: 'Game session started!'
                    });
                    return {
                        success: true,
                        session
                    };
                } else {
                    throw new Error(response.error || 'Failed to start game session');
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : 'Failed to start game session';
                addNotification({
                    type: 'error',
                    message
                });
                return {
                    success: false,
                    error: message
                };
            }
        }
    }["useGameSessionActions.useCallback[startGameSession]"], [
        setCurrentSession,
        addNotification
    ]);
    const endGameSession = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useGameSessionActions.useCallback[endGameSession]": async (sessionId)=>{
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].endGameSession(sessionId);
                if (response.success) {
                    setCurrentSession(null);
                    setCurrentDungeon(null);
                    setCurrentFloor(null);
                    setGameState(null);
                    addNotification({
                        type: 'info',
                        message: 'Game session ended'
                    });
                    return {
                        success: true
                    };
                } else {
                    throw new Error(response.error || 'Failed to end game session');
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : 'Failed to end game session';
                addNotification({
                    type: 'error',
                    message
                });
                return {
                    success: false,
                    error: message
                };
            }
        }
    }["useGameSessionActions.useCallback[endGameSession]"], [
        setCurrentSession,
        setCurrentDungeon,
        setCurrentFloor,
        setGameState,
        addNotification
    ]);
    return {
        startGameSession,
        endGameSession
    };
}
_s2(useGameSessionActions, "raz+fmUFE7HKDogK1jBxLVN9+Po=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGameStore"]
    ];
});
function useCombatActions() {
    _s3();
    const { setCurrentCombat, setCombatLoading, setLastAction, updateCharacter, addNotification } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGameStore"])();
    const startCombat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCombatActions.useCallback[startCombat]": async (sessionId, enemyId)=>{
            setCombatLoading(true);
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].startCombat(sessionId, enemyId);
                if (response.success && response.data) {
                    const combat = response.data;
                    setCurrentCombat(combat);
                    addNotification({
                        type: 'info',
                        message: 'Combat started!'
                    });
                    return {
                        success: true,
                        combat
                    };
                } else {
                    throw new Error(response.error || 'Failed to start combat');
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : 'Failed to start combat';
                addNotification({
                    type: 'error',
                    message
                });
                return {
                    success: false,
                    error: message
                };
            } finally{
                setCombatLoading(false);
            }
        }
    }["useCombatActions.useCallback[startCombat]"], [
        setCurrentCombat,
        setCombatLoading,
        addNotification
    ]);
    const performAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCombatActions.useCallback[performAction]": async (combatId, action)=>{
            setCombatLoading(true);
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].performCombatAction(combatId, action);
                if (response.success && response.data) {
                    const { combat, character } = response.data;
                    setCurrentCombat(combat);
                    setLastAction(action);
                    if (character) {
                        updateCharacter(character);
                    }
                    // Check if combat ended
                    if (!combat.is_active) {
                        const winner = combat.winner;
                        addNotification({
                            type: winner === 'character' ? 'success' : 'error',
                            message: winner === 'character' ? 'Victory!' : 'Defeat!'
                        });
                    }
                    return {
                        success: true,
                        combat
                    };
                } else {
                    throw new Error(response.error || 'Failed to perform action');
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : 'Failed to perform action';
                addNotification({
                    type: 'error',
                    message
                });
                return {
                    success: false,
                    error: message
                };
            } finally{
                setCombatLoading(false);
            }
        }
    }["useCombatActions.useCallback[performAction]"], [
        setCurrentCombat,
        setLastAction,
        updateCharacter,
        setCombatLoading,
        addNotification
    ]);
    return {
        startCombat,
        performAction
    };
}
_s3(useCombatActions, "oFT2+q8hsvxFP8KE1udZ900tvbw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGameStore"]
    ];
});
function useNotifications() {
    _s4();
    const { notifications, removeNotification, clearNotifications } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUI"])();
    const dismissNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNotifications.useCallback[dismissNotification]": (id)=>{
            removeNotification(id);
        }
    }["useNotifications.useCallback[dismissNotification]"], [
        removeNotification
    ]);
    const clearAll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNotifications.useCallback[clearAll]": ()=>{
            clearNotifications();
        }
    }["useNotifications.useCallback[clearAll]"], [
        clearNotifications
    ]);
    return {
        notifications,
        dismissNotification,
        clearAll
    };
}
_s4(useNotifications, "b+1FND8xm+Ls0Pd2tUtCk0Lukk4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUI"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/AuthScreen.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>AuthScreen)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useGameActions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useGameActions.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function AuthScreen() {
    _s();
    const [isLogin, setIsLogin] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [formData, setFormData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        email: '',
        username: '',
        password: ''
    });
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const { login, register } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useGameActions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthActions"])();
    const handleSubmit = async (e)=>{
        e.preventDefault();
        setIsLoading(true);
        try {
            if (isLogin) {
                await login({
                    email: formData.email,
                    password: formData.password
                });
            } else {
                const result = await register({
                    email: formData.email,
                    username: formData.username,
                    password: formData.password
                });
                if (result.success) {
                    setIsLogin(true);
                    setFormData({
                        ...formData,
                        password: ''
                    });
                }
            }
        } finally{
            setIsLoading(false);
        }
    };
    const handleInputChange = (e)=>{
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        });
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-black/20 backdrop-blur-sm border border-white/10 rounded-lg p-8 w-full max-w-md",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center mb-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-4xl font-bold text-white mb-2",
                            children: "Blackbird"
                        }, void 0, false, {
                            fileName: "[project]/src/components/AuthScreen.tsx",
                            lineNumber: 52,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-300",
                            children: "Turn-based Dungeon Crawler"
                        }, void 0, false, {
                            fileName: "[project]/src/components/AuthScreen.tsx",
                            lineNumber: 53,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/AuthScreen.tsx",
                    lineNumber: 51,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex mb-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            type: "button",
                            onClick: ()=>setIsLogin(true),
                            className: `flex-1 py-2 px-4 rounded-l-lg font-medium transition-colors ${isLogin ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`,
                            children: "Login"
                        }, void 0, false, {
                            fileName: "[project]/src/components/AuthScreen.tsx",
                            lineNumber: 57,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            type: "button",
                            onClick: ()=>setIsLogin(false),
                            className: `flex-1 py-2 px-4 rounded-r-lg font-medium transition-colors ${!isLogin ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`,
                            children: "Register"
                        }, void 0, false, {
                            fileName: "[project]/src/components/AuthScreen.tsx",
                            lineNumber: 68,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/AuthScreen.tsx",
                    lineNumber: 56,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                    onSubmit: handleSubmit,
                    className: "space-y-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: "email",
                                    className: "block text-sm font-medium text-gray-300 mb-1",
                                    children: "Email"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/AuthScreen.tsx",
                                    lineNumber: 83,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "email",
                                    id: "email",
                                    name: "email",
                                    value: formData.email,
                                    onChange: handleInputChange,
                                    required: true,
                                    className: "w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                                    placeholder: "Enter your email"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/AuthScreen.tsx",
                                    lineNumber: 86,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/AuthScreen.tsx",
                            lineNumber: 82,
                            columnNumber: 11
                        }, this),
                        !isLogin && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: "username",
                                    className: "block text-sm font-medium text-gray-300 mb-1",
                                    children: "Username"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/AuthScreen.tsx",
                                    lineNumber: 100,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "text",
                                    id: "username",
                                    name: "username",
                                    value: formData.username,
                                    onChange: handleInputChange,
                                    required: true,
                                    className: "w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                                    placeholder: "Choose a username"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/AuthScreen.tsx",
                                    lineNumber: 103,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/AuthScreen.tsx",
                            lineNumber: 99,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: "password",
                                    className: "block text-sm font-medium text-gray-300 mb-1",
                                    children: "Password"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/AuthScreen.tsx",
                                    lineNumber: 117,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "password",
                                    id: "password",
                                    name: "password",
                                    value: formData.password,
                                    onChange: handleInputChange,
                                    required: true,
                                    className: "w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                                    placeholder: "Enter your password"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/AuthScreen.tsx",
                                    lineNumber: 120,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/AuthScreen.tsx",
                            lineNumber: 116,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            type: "submit",
                            disabled: isLoading,
                            className: "w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-lg transition-colors",
                            children: isLoading ? 'Loading...' : isLogin ? 'Login' : 'Register'
                        }, void 0, false, {
                            fileName: "[project]/src/components/AuthScreen.tsx",
                            lineNumber: 132,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/AuthScreen.tsx",
                    lineNumber: 81,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mt-6 text-center",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        onClick: async ()=>{
                            // Quick demo setup
                            setIsLoading(true);
                            try {
                                // Try to initialize database
                                await fetch('/api/init-db', {
                                    method: 'POST'
                                });
                                await fetch('/api/seed-data', {
                                    method: 'POST'
                                });
                                // Create demo account
                                const demoResult = await register({
                                    email: '<EMAIL>',
                                    username: 'demo',
                                    password: 'demo123'
                                });
                                if (demoResult.success) {
                                    await login({
                                        email: '<EMAIL>',
                                        password: 'demo123'
                                    });
                                }
                            } catch (error) {
                                console.error('Demo setup failed:', error);
                            } finally{
                                setIsLoading(false);
                            }
                        },
                        className: "text-sm text-gray-400 hover:text-gray-300 transition-colors",
                        children: "Quick Demo Setup"
                    }, void 0, false, {
                        fileName: "[project]/src/components/AuthScreen.tsx",
                        lineNumber: 142,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/AuthScreen.tsx",
                    lineNumber: 141,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/AuthScreen.tsx",
            lineNumber: 50,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/AuthScreen.tsx",
        lineNumber: 49,
        columnNumber: 5
    }, this);
}
_s(AuthScreen, "rt9aPafbwmzFtNircJIKnWjkSb4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useGameActions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthActions"]
    ];
});
_c = AuthScreen;
var _c;
__turbopack_context__.k.register(_c, "AuthScreen");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/CharacterSelect.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CharacterSelect)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/gameStore.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useGameActions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useGameActions.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function CharacterSelect() {
    _s();
    const { characters, isLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCharacter"])();
    const { loadCharacters, createCharacter, selectCharacter } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useGameActions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCharacterActions"])();
    const { logout } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useGameActions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthActions"])();
    const [showCreateForm, setShowCreateForm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [newCharacterName, setNewCharacterName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [isCreating, setIsCreating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CharacterSelect.useEffect": ()=>{
            loadCharacters();
        }
    }["CharacterSelect.useEffect"], [
        loadCharacters
    ]);
    const handleCreateCharacter = async (e)=>{
        e.preventDefault();
        if (!newCharacterName.trim()) return;
        setIsCreating(true);
        try {
            const result = await createCharacter({
                name: newCharacterName.trim()
            });
            if (result.success) {
                setNewCharacterName('');
                setShowCreateForm(false);
            }
        } finally{
            setIsCreating(false);
        }
    };
    const handleSelectCharacter = (character)=>{
        selectCharacter(character);
    };
    if (isLoading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-white text-xl",
                children: "Loading characters..."
            }, void 0, false, {
                fileName: "[project]/src/components/CharacterSelect.tsx",
                lineNumber: 43,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/CharacterSelect.tsx",
            lineNumber: 42,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-4xl mx-auto",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex justify-between items-center mb-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-4xl font-bold text-white",
                            children: "Select Character"
                        }, void 0, false, {
                            fileName: "[project]/src/components/CharacterSelect.tsx",
                            lineNumber: 52,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: logout,
                            className: "px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors",
                            children: "Logout"
                        }, void 0, false, {
                            fileName: "[project]/src/components/CharacterSelect.tsx",
                            lineNumber: 53,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/CharacterSelect.tsx",
                    lineNumber: 51,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
                    children: [
                        characters.map((character)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-black/20 backdrop-blur-sm border border-white/10 rounded-lg p-6 hover:bg-black/30 transition-colors cursor-pointer",
                                onClick: ()=>handleSelectCharacter(character),
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full mx-auto mb-4 flex items-center justify-center",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-2xl font-bold text-white",
                                                children: character.name.charAt(0).toUpperCase()
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/CharacterSelect.tsx",
                                                lineNumber: 70,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/CharacterSelect.tsx",
                                            lineNumber: 69,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "text-xl font-bold text-white mb-2",
                                            children: character.name
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/CharacterSelect.tsx",
                                            lineNumber: 74,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-gray-300 space-y-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "Level ",
                                                        character.level
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/CharacterSelect.tsx",
                                                    lineNumber: 76,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "HP: ",
                                                        character.health,
                                                        "/",
                                                        character.max_health
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/CharacterSelect.tsx",
                                                    lineNumber: 77,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "Gold: ",
                                                        character.gold
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/CharacterSelect.tsx",
                                                    lineNumber: 78,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/CharacterSelect.tsx",
                                            lineNumber: 75,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "mt-4 grid grid-cols-2 gap-2 text-sm text-gray-400",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        "ATK: ",
                                                        character.attack
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/CharacterSelect.tsx",
                                                    lineNumber: 81,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        "DEF: ",
                                                        character.defense
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/CharacterSelect.tsx",
                                                    lineNumber: 82,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        "SPD: ",
                                                        character.speed
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/CharacterSelect.tsx",
                                                    lineNumber: 83,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        "EXP: ",
                                                        character.experience
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/CharacterSelect.tsx",
                                                    lineNumber: 84,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/CharacterSelect.tsx",
                                            lineNumber: 80,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/CharacterSelect.tsx",
                                    lineNumber: 68,
                                    columnNumber: 15
                                }, this)
                            }, character.id, false, {
                                fileName: "[project]/src/components/CharacterSelect.tsx",
                                lineNumber: 63,
                                columnNumber: 13
                            }, this)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-black/10 backdrop-blur-sm border-2 border-dashed border-white/20 rounded-lg p-6 hover:bg-black/20 transition-colors cursor-pointer flex items-center justify-center",
                            onClick: ()=>setShowCreateForm(true),
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-16 h-16 bg-gray-600 rounded-full mx-auto mb-4 flex items-center justify-center",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-3xl text-white",
                                            children: "+"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/CharacterSelect.tsx",
                                            lineNumber: 97,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/CharacterSelect.tsx",
                                        lineNumber: 96,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-white font-medium",
                                        children: "Create New Character"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/CharacterSelect.tsx",
                                        lineNumber: 99,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/CharacterSelect.tsx",
                                lineNumber: 95,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/CharacterSelect.tsx",
                            lineNumber: 91,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/CharacterSelect.tsx",
                    lineNumber: 61,
                    columnNumber: 9
                }, this),
                showCreateForm && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-gray-900 rounded-lg p-6 w-full max-w-md",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-2xl font-bold text-white mb-4",
                                children: "Create New Character"
                            }, void 0, false, {
                                fileName: "[project]/src/components/CharacterSelect.tsx",
                                lineNumber: 108,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                                onSubmit: handleCreateCharacter,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mb-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                htmlFor: "characterName",
                                                className: "block text-sm font-medium text-gray-300 mb-2",
                                                children: "Character Name"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/CharacterSelect.tsx",
                                                lineNumber: 112,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                type: "text",
                                                id: "characterName",
                                                value: newCharacterName,
                                                onChange: (e)=>setNewCharacterName(e.target.value),
                                                className: "w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                                                placeholder: "Enter character name",
                                                maxLength: 50,
                                                required: true
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/CharacterSelect.tsx",
                                                lineNumber: 115,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/CharacterSelect.tsx",
                                        lineNumber: 111,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex gap-3",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                type: "button",
                                                onClick: ()=>{
                                                    setShowCreateForm(false);
                                                    setNewCharacterName('');
                                                },
                                                className: "flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors",
                                                children: "Cancel"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/CharacterSelect.tsx",
                                                lineNumber: 128,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                type: "submit",
                                                disabled: isCreating || !newCharacterName.trim(),
                                                className: "flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors",
                                                children: isCreating ? 'Creating...' : 'Create'
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/CharacterSelect.tsx",
                                                lineNumber: 138,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/CharacterSelect.tsx",
                                        lineNumber: 127,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/CharacterSelect.tsx",
                                lineNumber: 110,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/CharacterSelect.tsx",
                        lineNumber: 107,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/CharacterSelect.tsx",
                    lineNumber: 106,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/CharacterSelect.tsx",
            lineNumber: 50,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/CharacterSelect.tsx",
        lineNumber: 49,
        columnNumber: 5
    }, this);
}
_s(CharacterSelect, "oEGVZsXD03zWatUSNRcONHf46fY=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCharacter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useGameActions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCharacterActions"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useGameActions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthActions"]
    ];
});
_c = CharacterSelect;
var _c;
__turbopack_context__.k.register(_c, "CharacterSelect");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/GameScreen.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>GameScreen)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/gameStore.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useGameActions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useGameActions.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
'use client';
;
;
function GameScreen() {
    _s();
    const { currentView, setCurrentView } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGameStore"])();
    const { currentCharacter } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCharacter"])();
    const { isInCombat } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCombat"])();
    const { logout } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useGameActions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthActions"])();
    if (!currentCharacter) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-white text-xl",
                children: "No character selected"
            }, void 0, false, {
                fileName: "[project]/src/components/GameScreen.tsx",
                lineNumber: 15,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/GameScreen.tsx",
            lineNumber: 14,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-black/20 backdrop-blur-sm border-b border-white/10 p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-6xl mx-auto flex justify-between items-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                    className: "text-2xl font-bold text-white",
                                    children: currentCharacter.name
                                }, void 0, false, {
                                    fileName: "[project]/src/components/GameScreen.tsx",
                                    lineNumber: 26,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center space-x-4 text-sm text-gray-300",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: [
                                                "Level ",
                                                currentCharacter.level
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/GameScreen.tsx",
                                            lineNumber: 28,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: [
                                                "HP: ",
                                                currentCharacter.health,
                                                "/",
                                                currentCharacter.max_health
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/GameScreen.tsx",
                                            lineNumber: 29,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: [
                                                "Gold: ",
                                                currentCharacter.gold
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/GameScreen.tsx",
                                            lineNumber: 30,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/GameScreen.tsx",
                                    lineNumber: 27,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/GameScreen.tsx",
                            lineNumber: 25,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>setCurrentView('character-select'),
                                    className: "px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors",
                                    children: "Change Character"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/GameScreen.tsx",
                                    lineNumber: 35,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: logout,
                                    className: "px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors",
                                    children: "Logout"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/GameScreen.tsx",
                                    lineNumber: 41,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/GameScreen.tsx",
                            lineNumber: 34,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/GameScreen.tsx",
                    lineNumber: 24,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/GameScreen.tsx",
                lineNumber: 23,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-black/10 backdrop-blur-sm border-b border-white/10 p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-6xl mx-auto",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                        className: "flex space-x-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>setCurrentView('dungeon'),
                                className: `px-4 py-2 rounded-lg transition-colors ${currentView === 'dungeon' ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`,
                                children: "Dungeon"
                            }, void 0, false, {
                                fileName: "[project]/src/components/GameScreen.tsx",
                                lineNumber: 55,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>setCurrentView('inventory'),
                                className: `px-4 py-2 rounded-lg transition-colors ${currentView === 'inventory' ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`,
                                children: "Inventory"
                            }, void 0, false, {
                                fileName: "[project]/src/components/GameScreen.tsx",
                                lineNumber: 65,
                                columnNumber: 13
                            }, this),
                            isInCombat && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>setCurrentView('combat'),
                                className: `px-4 py-2 rounded-lg transition-colors ${currentView === 'combat' ? 'bg-red-600 text-white' : 'bg-red-700 text-red-300 hover:bg-red-600'}`,
                                children: "Combat!"
                            }, void 0, false, {
                                fileName: "[project]/src/components/GameScreen.tsx",
                                lineNumber: 76,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/GameScreen.tsx",
                        lineNumber: 54,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/GameScreen.tsx",
                    lineNumber: 53,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/GameScreen.tsx",
                lineNumber: 52,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "max-w-6xl mx-auto p-4",
                children: [
                    currentView === 'dungeon' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DungeonView, {}, void 0, false, {
                        fileName: "[project]/src/components/GameScreen.tsx",
                        lineNumber: 93,
                        columnNumber: 39
                    }, this),
                    currentView === 'inventory' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(InventoryView, {}, void 0, false, {
                        fileName: "[project]/src/components/GameScreen.tsx",
                        lineNumber: 94,
                        columnNumber: 41
                    }, this),
                    currentView === 'combat' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(CombatView, {}, void 0, false, {
                        fileName: "[project]/src/components/GameScreen.tsx",
                        lineNumber: 95,
                        columnNumber: 38
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/GameScreen.tsx",
                lineNumber: 92,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/GameScreen.tsx",
        lineNumber: 21,
        columnNumber: 5
    }, this);
}
_s(GameScreen, "StMID9EKV0c3/UfBRzlcjtN1CUM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGameStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCharacter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCombat"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useGameActions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthActions"]
    ];
});
_c = GameScreen;
function DungeonView() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-black/20 backdrop-blur-sm border border-white/10 rounded-lg p-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                className: "text-2xl font-bold text-white mb-4",
                children: "Dungeon Explorer"
            }, void 0, false, {
                fileName: "[project]/src/components/GameScreen.tsx",
                lineNumber: 104,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-gray-300",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mb-4",
                        children: "Welcome to the dungeon! This is where you'll explore procedurally generated dungeons, fight enemies, and find treasure."
                    }, void 0, false, {
                        fileName: "[project]/src/components/GameScreen.tsx",
                        lineNumber: 106,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-gray-800 rounded-lg p-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-yellow-400 font-medium",
                                children: "Coming Soon:"
                            }, void 0, false, {
                                fileName: "[project]/src/components/GameScreen.tsx",
                                lineNumber: 108,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                className: "mt-2 space-y-1 text-sm",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                        children: "• Procedural dungeon generation"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/GameScreen.tsx",
                                        lineNumber: 110,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                        children: "• Room exploration"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/GameScreen.tsx",
                                        lineNumber: 111,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                        children: "• Enemy encounters"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/GameScreen.tsx",
                                        lineNumber: 112,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                        children: "• Treasure chests"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/GameScreen.tsx",
                                        lineNumber: 113,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                        children: "• Boss battles"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/GameScreen.tsx",
                                        lineNumber: 114,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/GameScreen.tsx",
                                lineNumber: 109,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/GameScreen.tsx",
                        lineNumber: 107,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/GameScreen.tsx",
                lineNumber: 105,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/GameScreen.tsx",
        lineNumber: 103,
        columnNumber: 5
    }, this);
}
_c1 = DungeonView;
function InventoryView() {
    _s1();
    const { characterInventory } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCharacter"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-black/20 backdrop-blur-sm border border-white/10 rounded-lg p-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                className: "text-2xl font-bold text-white mb-4",
                children: "Inventory"
            }, void 0, false, {
                fileName: "[project]/src/components/GameScreen.tsx",
                lineNumber: 127,
                columnNumber: 7
            }, this),
            characterInventory.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-gray-400 text-center py-8",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    children: "Your inventory is empty."
                }, void 0, false, {
                    fileName: "[project]/src/components/GameScreen.tsx",
                    lineNumber: 131,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/GameScreen.tsx",
                lineNumber: 130,
                columnNumber: 9
            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",
                children: characterInventory.map((inventoryItem)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: `bg-gray-800 rounded-lg p-4 border-2 ${inventoryItem.is_equipped ? 'border-green-500' : 'border-gray-600'}`,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between items-start mb-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "font-bold text-white",
                                        children: inventoryItem.item.name
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/GameScreen.tsx",
                                        lineNumber: 145,
                                        columnNumber: 17
                                    }, this),
                                    inventoryItem.is_equipped && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-xs bg-green-600 text-white px-2 py-1 rounded",
                                        children: "Equipped"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/GameScreen.tsx",
                                        lineNumber: 147,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/GameScreen.tsx",
                                lineNumber: 144,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-gray-400 mb-2 capitalize",
                                children: [
                                    inventoryItem.item.type,
                                    " • ",
                                    inventoryItem.item.rarity
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/GameScreen.tsx",
                                lineNumber: 153,
                                columnNumber: 15
                            }, this),
                            inventoryItem.item.description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-gray-300 mb-3",
                                children: inventoryItem.item.description
                            }, void 0, false, {
                                fileName: "[project]/src/components/GameScreen.tsx",
                                lineNumber: 158,
                                columnNumber: 17
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-xs text-gray-400 space-y-1",
                                children: [
                                    inventoryItem.item.attack_bonus > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            "Attack: +",
                                            inventoryItem.item.attack_bonus
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/GameScreen.tsx",
                                        lineNumber: 165,
                                        columnNumber: 19
                                    }, this),
                                    inventoryItem.item.defense_bonus > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            "Defense: +",
                                            inventoryItem.item.defense_bonus
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/GameScreen.tsx",
                                        lineNumber: 168,
                                        columnNumber: 19
                                    }, this),
                                    inventoryItem.item.health_bonus > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            "Health: +",
                                            inventoryItem.item.health_bonus
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/GameScreen.tsx",
                                        lineNumber: 171,
                                        columnNumber: 19
                                    }, this),
                                    inventoryItem.item.speed_bonus > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            "Speed: +",
                                            inventoryItem.item.speed_bonus
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/GameScreen.tsx",
                                        lineNumber: 174,
                                        columnNumber: 19
                                    }, this),
                                    inventoryItem.item.heal_amount > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            "Heals: ",
                                            inventoryItem.item.heal_amount,
                                            " HP"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/GameScreen.tsx",
                                        lineNumber: 177,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/GameScreen.tsx",
                                lineNumber: 163,
                                columnNumber: 15
                            }, this),
                            inventoryItem.quantity > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-2 text-sm text-yellow-400",
                                children: [
                                    "Quantity: ",
                                    inventoryItem.quantity
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/GameScreen.tsx",
                                lineNumber: 182,
                                columnNumber: 17
                            }, this)
                        ]
                    }, inventoryItem.id, true, {
                        fileName: "[project]/src/components/GameScreen.tsx",
                        lineNumber: 136,
                        columnNumber: 13
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/GameScreen.tsx",
                lineNumber: 134,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/GameScreen.tsx",
        lineNumber: 126,
        columnNumber: 5
    }, this);
}
_s1(InventoryView, "34ZP2xlrqLrdcU0WE8SiSND/x4c=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCharacter"]
    ];
});
_c2 = InventoryView;
function CombatView() {
    _s2();
    const { currentCombat } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCombat"])();
    if (!currentCombat) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-black/20 backdrop-blur-sm border border-white/10 rounded-lg p-6",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                    className: "text-2xl font-bold text-white mb-4",
                    children: "Combat"
                }, void 0, false, {
                    fileName: "[project]/src/components/GameScreen.tsx",
                    lineNumber: 200,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-gray-300",
                    children: "No active combat session."
                }, void 0, false, {
                    fileName: "[project]/src/components/GameScreen.tsx",
                    lineNumber: 201,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/GameScreen.tsx",
            lineNumber: 199,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-black/20 backdrop-blur-sm border border-white/10 rounded-lg p-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                className: "text-2xl font-bold text-white mb-4",
                children: "Combat"
            }, void 0, false, {
                fileName: "[project]/src/components/GameScreen.tsx",
                lineNumber: 208,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-gray-300",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mb-4",
                        children: [
                            "Combat system is ready! You're fighting: ",
                            currentCombat.enemy_data.name
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/GameScreen.tsx",
                        lineNumber: 210,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-gray-800 rounded-lg p-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-red-400 font-medium",
                                children: "Combat Features:"
                            }, void 0, false, {
                                fileName: "[project]/src/components/GameScreen.tsx",
                                lineNumber: 212,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                className: "mt-2 space-y-1 text-sm",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                        children: "• Turn-based combat"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/GameScreen.tsx",
                                        lineNumber: 214,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                        children: "• Attack, defend, and use items"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/GameScreen.tsx",
                                        lineNumber: 215,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                        children: "• Status effects"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/GameScreen.tsx",
                                        lineNumber: 216,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                        children: "• Experience and gold rewards"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/GameScreen.tsx",
                                        lineNumber: 217,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/GameScreen.tsx",
                                lineNumber: 213,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/GameScreen.tsx",
                        lineNumber: 211,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/GameScreen.tsx",
                lineNumber: 209,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/GameScreen.tsx",
        lineNumber: 207,
        columnNumber: 5
    }, this);
}
_s2(CombatView, "FHxb/kuKaU9DAtaIO1Hq5qBgUo8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCombat"]
    ];
});
_c3 = CombatView;
var _c, _c1, _c2, _c3;
__turbopack_context__.k.register(_c, "GameScreen");
__turbopack_context__.k.register(_c1, "DungeonView");
__turbopack_context__.k.register(_c2, "InventoryView");
__turbopack_context__.k.register(_c3, "CombatView");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Home)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/gameStore.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AuthScreen$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/AuthScreen.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$CharacterSelect$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/CharacterSelect.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$GameScreen$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/GameScreen.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
function Home() {
    _s();
    const { isAuthenticated, token, currentView, syncWithServer } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGameStore"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Home.useEffect": ()=>{
            // Set token in API client if user is authenticated
            if (token) {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].setToken(token);
            }
        }
    }["Home.useEffect"], [
        token
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Home.useEffect": ()=>{
            // Sync with server periodically if authenticated
            if (isAuthenticated) {
                const interval = setInterval(syncWithServer, 30000); // Every 30 seconds
                return ({
                    "Home.useEffect": ()=>clearInterval(interval)
                })["Home.useEffect"];
            }
        }
    }["Home.useEffect"], [
        isAuthenticated,
        syncWithServer
    ]);
    if (!isAuthenticated) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AuthScreen$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
            fileName: "[project]/src/app/page.tsx",
            lineNumber: 30,
            columnNumber: 12
        }, this);
    }
    switch(currentView){
        case "character-select":
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$CharacterSelect$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 35,
                columnNumber: 14
            }, this);
        case "dungeon":
        case "combat":
        case "inventory":
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$GameScreen$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 39,
                columnNumber: 14
            }, this);
        default:
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$CharacterSelect$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 41,
                columnNumber: 14
            }, this);
    }
}
_s(Home, "j6hbdrP3USY/14NhqRD9EIvZWdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$gameStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useGameStore"]
    ];
});
_c = Home;
var _c;
__turbopack_context__.k.register(_c, "Home");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_9243e1e5._.js.map