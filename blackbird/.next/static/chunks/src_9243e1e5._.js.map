{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/projects/blackbird/src/store/gameStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { devtools, persist } from 'zustand/middleware';\nimport { \n  User, \n  Character, \n  GameSession, \n  CombatSession, \n  InventoryItem,\n  GameState,\n  Dungeon,\n  DungeonFloor\n} from '../types/game';\n\ninterface AuthState {\n  user: User | null;\n  token: string | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n}\n\ninterface CharacterState {\n  characters: Character[];\n  currentCharacter: Character | null;\n  characterInventory: InventoryItem[];\n  isLoading: boolean;\n}\n\ninterface GameSessionState {\n  currentSession: GameSession | null;\n  currentDungeon: Dungeon | null;\n  currentFloor: DungeonFloor | null;\n  gameState: GameState | null;\n  isLoading: boolean;\n}\n\ninterface CombatState {\n  currentCombat: CombatSession | null;\n  isInCombat: boolean;\n  isLoading: boolean;\n  lastAction: any;\n}\n\ninterface UIState {\n  currentView: 'menu' | 'character-select' | 'dungeon' | 'combat' | 'inventory';\n  showInventory: boolean;\n  notifications: Array<{\n    id: string;\n    type: 'success' | 'error' | 'info' | 'warning';\n    message: string;\n    timestamp: number;\n  }>;\n}\n\ninterface GameStore extends AuthState, CharacterState, GameSessionState, CombatState, UIState {\n  // Auth actions\n  setAuth: (user: User, token: string) => void;\n  logout: () => void;\n  setAuthLoading: (loading: boolean) => void;\n\n  // Character actions\n  setCharacters: (characters: Character[]) => void;\n  setCurrentCharacter: (character: Character | null) => void;\n  updateCharacter: (character: Character) => void;\n  setCharacterInventory: (inventory: InventoryItem[]) => void;\n  updateInventoryItem: (item: InventoryItem) => void;\n  removeInventoryItem: (itemId: string) => void;\n  setCharacterLoading: (loading: boolean) => void;\n\n  // Game session actions\n  setCurrentSession: (session: GameSession | null) => void;\n  setCurrentDungeon: (dungeon: Dungeon | null) => void;\n  setCurrentFloor: (floor: DungeonFloor | null) => void;\n  setGameState: (state: GameState | null) => void;\n  setGameLoading: (loading: boolean) => void;\n\n  // Combat actions\n  setCurrentCombat: (combat: CombatSession | null) => void;\n  setInCombat: (inCombat: boolean) => void;\n  setCombatLoading: (loading: boolean) => void;\n  setLastAction: (action: any) => void;\n\n  // UI actions\n  setCurrentView: (view: UIState['currentView']) => void;\n  setShowInventory: (show: boolean) => void;\n  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => void;\n  removeNotification: (id: string) => void;\n  clearNotifications: () => void;\n\n  // Optimistic updates\n  optimisticCharacterUpdate: (updates: Partial<Character>) => void;\n  revertOptimisticUpdate: () => void;\n\n  // State synchronization\n  syncWithServer: () => Promise<void>;\n}\n\nexport const useGameStore = create<GameStore>()(\n  devtools(\n    persist(\n      (set, get) => ({\n        // Initial state\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        \n        characters: [],\n        currentCharacter: null,\n        characterInventory: [],\n        \n        currentSession: null,\n        currentDungeon: null,\n        currentFloor: null,\n        gameState: null,\n        \n        currentCombat: null,\n        isInCombat: false,\n        lastAction: null,\n        \n        currentView: 'menu',\n        showInventory: false,\n        notifications: [],\n\n        // Auth actions\n        setAuth: (user, token) => set({\n          user,\n          token,\n          isAuthenticated: true,\n          isLoading: false\n        }),\n\n        logout: () => set({\n          user: null,\n          token: null,\n          isAuthenticated: false,\n          currentCharacter: null,\n          characters: [],\n          characterInventory: [],\n          currentSession: null,\n          currentDungeon: null,\n          currentFloor: null,\n          gameState: null,\n          currentCombat: null,\n          isInCombat: false,\n          currentView: 'menu'\n        }),\n\n        setAuthLoading: (loading) => set({ isLoading: loading }),\n\n        // Character actions\n        setCharacters: (characters) => set({ characters }),\n        \n        setCurrentCharacter: (character) => set({ \n          currentCharacter: character,\n          currentView: character ? 'dungeon' : 'character-select'\n        }),\n\n        updateCharacter: (character) => set((state) => ({\n          currentCharacter: state.currentCharacter?.id === character.id ? character : state.currentCharacter,\n          characters: state.characters.map(c => c.id === character.id ? character : c)\n        })),\n\n        setCharacterInventory: (inventory) => set({ characterInventory: inventory }),\n\n        updateInventoryItem: (item) => set((state) => ({\n          characterInventory: state.characterInventory.map(i => i.id === item.id ? item : i)\n        })),\n\n        removeInventoryItem: (itemId) => set((state) => ({\n          characterInventory: state.characterInventory.filter(i => i.id !== itemId)\n        })),\n\n        setCharacterLoading: (loading) => set({ isLoading: loading }),\n\n        // Game session actions\n        setCurrentSession: (session) => set({ currentSession: session }),\n        setCurrentDungeon: (dungeon) => set({ currentDungeon: dungeon }),\n        setCurrentFloor: (floor) => set({ currentFloor: floor }),\n        setGameState: (state) => set({ gameState: state }),\n        setGameLoading: (loading) => set({ isLoading: loading }),\n\n        // Combat actions\n        setCurrentCombat: (combat) => set({ \n          currentCombat: combat,\n          isInCombat: !!combat,\n          currentView: combat ? 'combat' : 'dungeon'\n        }),\n\n        setInCombat: (inCombat) => set({ isInCombat: inCombat }),\n        setCombatLoading: (loading) => set({ isLoading: loading }),\n        setLastAction: (action) => set({ lastAction: action }),\n\n        // UI actions\n        setCurrentView: (view) => set({ currentView: view }),\n        setShowInventory: (show) => set({ showInventory: show }),\n\n        addNotification: (notification) => set((state) => ({\n          notifications: [...state.notifications, {\n            ...notification,\n            id: Date.now().toString(),\n            timestamp: Date.now()\n          }]\n        })),\n\n        removeNotification: (id) => set((state) => ({\n          notifications: state.notifications.filter(n => n.id !== id)\n        })),\n\n        clearNotifications: () => set({ notifications: [] }),\n\n        // Optimistic updates\n        optimisticCharacterUpdate: (updates) => set((state) => {\n          if (!state.currentCharacter) return state;\n          \n          const updatedCharacter = { ...state.currentCharacter, ...updates };\n          return {\n            currentCharacter: updatedCharacter,\n            characters: state.characters.map(c => \n              c.id === updatedCharacter.id ? updatedCharacter : c\n            )\n          };\n        }),\n\n        revertOptimisticUpdate: () => {\n          // This would revert to the last known server state\n          // Implementation depends on how you want to handle rollbacks\n        },\n\n        // State synchronization\n        syncWithServer: async () => {\n          const state = get();\n          if (!state.isAuthenticated || !state.token) return;\n\n          try {\n            // Sync character data\n            if (state.currentCharacter) {\n              const response = await fetch(`/api/characters/${state.currentCharacter.id}`, {\n                headers: {\n                  'Authorization': `Bearer ${state.token}`,\n                  'Content-Type': 'application/json'\n                }\n              });\n\n              if (response.ok) {\n                const { data: character } = await response.json();\n                set({ currentCharacter: character });\n              }\n            }\n\n            // Sync game session if active\n            if (state.currentSession) {\n              // Implement session sync logic\n            }\n\n            // Sync combat if active\n            if (state.currentCombat) {\n              // Implement combat sync logic\n            }\n\n          } catch (error) {\n            console.error('Failed to sync with server:', error);\n          }\n        }\n      }),\n      {\n        name: 'blackbird-game-store',\n        partialize: (state) => ({\n          user: state.user,\n          token: state.token,\n          isAuthenticated: state.isAuthenticated,\n          currentCharacter: state.currentCharacter,\n          currentView: state.currentView\n        })\n      }\n    ),\n    { name: 'GameStore' }\n  )\n);\n\n// Selectors for common state combinations\nexport const useAuth = () => useGameStore((state) => ({\n  user: state.user,\n  token: state.token,\n  isAuthenticated: state.isAuthenticated,\n  isLoading: state.isLoading,\n  setAuth: state.setAuth,\n  logout: state.logout,\n  setAuthLoading: state.setAuthLoading\n}));\n\nexport const useCharacter = () => useGameStore((state) => ({\n  characters: state.characters,\n  currentCharacter: state.currentCharacter,\n  characterInventory: state.characterInventory,\n  isLoading: state.isLoading,\n  setCharacters: state.setCharacters,\n  setCurrentCharacter: state.setCurrentCharacter,\n  updateCharacter: state.updateCharacter,\n  setCharacterInventory: state.setCharacterInventory,\n  updateInventoryItem: state.updateInventoryItem,\n  removeInventoryItem: state.removeInventoryItem\n}));\n\nexport const useCombat = () => useGameStore((state) => ({\n  currentCombat: state.currentCombat,\n  isInCombat: state.isInCombat,\n  isLoading: state.isLoading,\n  lastAction: state.lastAction,\n  setCurrentCombat: state.setCurrentCombat,\n  setInCombat: state.setInCombat,\n  setCombatLoading: state.setCombatLoading,\n  setLastAction: state.setLastAction\n}));\n\nexport const useUI = () => useGameStore((state) => ({\n  currentView: state.currentView,\n  showInventory: state.showInventory,\n  notifications: state.notifications,\n  setCurrentView: state.setCurrentView,\n  setShowInventory: state.setShowInventory,\n  addNotification: state.addNotification,\n  removeNotification: state.removeNotification,\n  clearNotifications: state.clearNotifications\n}));\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AA+FO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,OAAO;QACP,iBAAiB;QACjB,WAAW;QAEX,YAAY,EAAE;QACd,kBAAkB;QAClB,oBAAoB,EAAE;QAEtB,gBAAgB;QAChB,gBAAgB;QAChB,cAAc;QACd,WAAW;QAEX,eAAe;QACf,YAAY;QACZ,YAAY;QAEZ,aAAa;QACb,eAAe;QACf,eAAe,EAAE;QAEjB,eAAe;QACf,SAAS,CAAC,MAAM,QAAU,IAAI;gBAC5B;gBACA;gBACA,iBAAiB;gBACjB,WAAW;YACb;QAEA,QAAQ,IAAM,IAAI;gBAChB,MAAM;gBACN,OAAO;gBACP,iBAAiB;gBACjB,kBAAkB;gBAClB,YAAY,EAAE;gBACd,oBAAoB,EAAE;gBACtB,gBAAgB;gBAChB,gBAAgB;gBAChB,cAAc;gBACd,WAAW;gBACX,eAAe;gBACf,YAAY;gBACZ,aAAa;YACf;QAEA,gBAAgB,CAAC,UAAY,IAAI;gBAAE,WAAW;YAAQ;QAEtD,oBAAoB;QACpB,eAAe,CAAC,aAAe,IAAI;gBAAE;YAAW;QAEhD,qBAAqB,CAAC,YAAc,IAAI;gBACtC,kBAAkB;gBAClB,aAAa,YAAY,YAAY;YACvC;QAEA,iBAAiB,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC9C,kBAAkB,MAAM,gBAAgB,EAAE,OAAO,UAAU,EAAE,GAAG,YAAY,MAAM,gBAAgB;oBAClG,YAAY,MAAM,UAAU,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG,YAAY;gBAC5E,CAAC;QAED,uBAAuB,CAAC,YAAc,IAAI;gBAAE,oBAAoB;YAAU;QAE1E,qBAAqB,CAAC,OAAS,IAAI,CAAC,QAAU,CAAC;oBAC7C,oBAAoB,MAAM,kBAAkB,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE,GAAG,OAAO;gBAClF,CAAC;QAED,qBAAqB,CAAC,SAAW,IAAI,CAAC,QAAU,CAAC;oBAC/C,oBAAoB,MAAM,kBAAkB,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACpE,CAAC;QAED,qBAAqB,CAAC,UAAY,IAAI;gBAAE,WAAW;YAAQ;QAE3D,uBAAuB;QACvB,mBAAmB,CAAC,UAAY,IAAI;gBAAE,gBAAgB;YAAQ;QAC9D,mBAAmB,CAAC,UAAY,IAAI;gBAAE,gBAAgB;YAAQ;QAC9D,iBAAiB,CAAC,QAAU,IAAI;gBAAE,cAAc;YAAM;QACtD,cAAc,CAAC,QAAU,IAAI;gBAAE,WAAW;YAAM;QAChD,gBAAgB,CAAC,UAAY,IAAI;gBAAE,WAAW;YAAQ;QAEtD,iBAAiB;QACjB,kBAAkB,CAAC,SAAW,IAAI;gBAChC,eAAe;gBACf,YAAY,CAAC,CAAC;gBACd,aAAa,SAAS,WAAW;YACnC;QAEA,aAAa,CAAC,WAAa,IAAI;gBAAE,YAAY;YAAS;QACtD,kBAAkB,CAAC,UAAY,IAAI;gBAAE,WAAW;YAAQ;QACxD,eAAe,CAAC,SAAW,IAAI;gBAAE,YAAY;YAAO;QAEpD,aAAa;QACb,gBAAgB,CAAC,OAAS,IAAI;gBAAE,aAAa;YAAK;QAClD,kBAAkB,CAAC,OAAS,IAAI;gBAAE,eAAe;YAAK;QAEtD,iBAAiB,CAAC,eAAiB,IAAI,CAAC,QAAU,CAAC;oBACjD,eAAe;2BAAI,MAAM,aAAa;wBAAE;4BACtC,GAAG,YAAY;4BACf,IAAI,KAAK,GAAG,GAAG,QAAQ;4BACvB,WAAW,KAAK,GAAG;wBACrB;qBAAE;gBACJ,CAAC;QAED,oBAAoB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAC1C,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC1D,CAAC;QAED,oBAAoB,IAAM,IAAI;gBAAE,eAAe,EAAE;YAAC;QAElD,qBAAqB;QACrB,2BAA2B,CAAC,UAAY,IAAI,CAAC;gBAC3C,IAAI,CAAC,MAAM,gBAAgB,EAAE,OAAO;gBAEpC,MAAM,mBAAmB;oBAAE,GAAG,MAAM,gBAAgB;oBAAE,GAAG,OAAO;gBAAC;gBACjE,OAAO;oBACL,kBAAkB;oBAClB,YAAY,MAAM,UAAU,CAAC,GAAG,CAAC,CAAA,IAC/B,EAAE,EAAE,KAAK,iBAAiB,EAAE,GAAG,mBAAmB;gBAEtD;YACF;QAEA,wBAAwB;QACtB,mDAAmD;QACnD,6DAA6D;QAC/D;QAEA,wBAAwB;QACxB,gBAAgB;YACd,MAAM,QAAQ;YACd,IAAI,CAAC,MAAM,eAAe,IAAI,CAAC,MAAM,KAAK,EAAE;YAE5C,IAAI;gBACF,sBAAsB;gBACtB,IAAI,MAAM,gBAAgB,EAAE;oBAC1B,MAAM,WAAW,MAAM,MAAM,CAAC,gBAAgB,EAAE,MAAM,gBAAgB,CAAC,EAAE,EAAE,EAAE;wBAC3E,SAAS;4BACP,iBAAiB,CAAC,OAAO,EAAE,MAAM,KAAK,EAAE;4BACxC,gBAAgB;wBAClB;oBACF;oBAEA,IAAI,SAAS,EAAE,EAAE;wBACf,MAAM,EAAE,MAAM,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI;wBAC/C,IAAI;4BAAE,kBAAkB;wBAAU;oBACpC;gBACF;gBAEA,8BAA8B;gBAC9B,IAAI,MAAM,cAAc,EAAE;gBACxB,+BAA+B;gBACjC;gBAEA,wBAAwB;gBACxB,IAAI,MAAM,aAAa,EAAE;gBACvB,8BAA8B;gBAChC;YAEF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;YAC/C;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,OAAO,MAAM,KAAK;YAClB,iBAAiB,MAAM,eAAe;YACtC,kBAAkB,MAAM,gBAAgB;YACxC,aAAa,MAAM,WAAW;QAChC,CAAC;AACH,IAEF;IAAE,MAAM;AAAY;AAKjB,MAAM,UAAU;;IAAM,OAAA;gCAAa,CAAC,QAAU,CAAC;gBACpD,MAAM,MAAM,IAAI;gBAChB,OAAO,MAAM,KAAK;gBAClB,iBAAiB,MAAM,eAAe;gBACtC,WAAW,MAAM,SAAS;gBAC1B,SAAS,MAAM,OAAO;gBACtB,QAAQ,MAAM,MAAM;gBACpB,gBAAgB,MAAM,cAAc;YACtC,CAAC;;AAAC;GARW;;QAAgB;;;AAUtB,MAAM,eAAe;;IAAM,OAAA;qCAAa,CAAC,QAAU,CAAC;gBACzD,YAAY,MAAM,UAAU;gBAC5B,kBAAkB,MAAM,gBAAgB;gBACxC,oBAAoB,MAAM,kBAAkB;gBAC5C,WAAW,MAAM,SAAS;gBAC1B,eAAe,MAAM,aAAa;gBAClC,qBAAqB,MAAM,mBAAmB;gBAC9C,iBAAiB,MAAM,eAAe;gBACtC,uBAAuB,MAAM,qBAAqB;gBAClD,qBAAqB,MAAM,mBAAmB;gBAC9C,qBAAqB,MAAM,mBAAmB;YAChD,CAAC;;AAAC;IAXW;;QAAqB;;;AAa3B,MAAM,YAAY;;IAAM,OAAA;kCAAa,CAAC,QAAU,CAAC;gBACtD,eAAe,MAAM,aAAa;gBAClC,YAAY,MAAM,UAAU;gBAC5B,WAAW,MAAM,SAAS;gBAC1B,YAAY,MAAM,UAAU;gBAC5B,kBAAkB,MAAM,gBAAgB;gBACxC,aAAa,MAAM,WAAW;gBAC9B,kBAAkB,MAAM,gBAAgB;gBACxC,eAAe,MAAM,aAAa;YACpC,CAAC;;AAAC;IATW;;QAAkB;;;AAWxB,MAAM,QAAQ;;IAAM,OAAA;8BAAa,CAAC,QAAU,CAAC;gBAClD,aAAa,MAAM,WAAW;gBAC9B,eAAe,MAAM,aAAa;gBAClC,eAAe,MAAM,aAAa;gBAClC,gBAAgB,MAAM,cAAc;gBACpC,kBAAkB,MAAM,gBAAgB;gBACxC,iBAAiB,MAAM,eAAe;gBACtC,oBAAoB,MAAM,kBAAkB;gBAC5C,oBAAoB,MAAM,kBAAkB;YAC9C,CAAC;;AAAC;IATW;;QAAc", "debugId": null}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/projects/blackbird/src/lib/api.ts"], "sourcesContent": ["import { ApiResponse } from '../types/game';\n\nclass ApiClient {\n  private baseUrl: string;\n  private token: string | null = null;\n\n  constructor(baseUrl: string = '/api') {\n    this.baseUrl = baseUrl;\n  }\n\n  setToken(token: string | null) {\n    this.token = token;\n  }\n\n  private getHeaders(): HeadersInit {\n    const headers: HeadersInit = {\n      'Content-Type': 'application/json',\n    };\n\n    if (this.token) {\n      headers['Authorization'] = `Bearer ${this.token}`;\n    }\n\n    return headers;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    const url = `${this.baseUrl}${endpoint}`;\n    \n    const config: RequestInit = {\n      ...options,\n      headers: {\n        ...this.getHeaders(),\n        ...options.headers,\n      },\n    };\n\n    try {\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || `HTTP error! status: ${response.status}`);\n      }\n\n      return data;\n    } catch (error) {\n      console.error(`API request failed: ${endpoint}`, error);\n      throw error;\n    }\n  }\n\n  // Auth endpoints\n  async register(email: string, username: string, password: string) {\n    return this.request('/auth/register', {\n      method: 'POST',\n      body: JSON.stringify({ email, username, password }),\n    });\n  }\n\n  async login(email: string, password: string) {\n    return this.request('/auth/login', {\n      method: 'POST',\n      body: JSON.stringify({ email, password }),\n    });\n  }\n\n  // Character endpoints\n  async getCharacters() {\n    return this.request('/characters');\n  }\n\n  async getCharacter(characterId: string) {\n    return this.request(`/characters/${characterId}`);\n  }\n\n  async createCharacter(name: string) {\n    return this.request('/characters', {\n      method: 'POST',\n      body: JSON.stringify({ name }),\n    });\n  }\n\n  async getCharacterInventory(characterId: string) {\n    return this.request(`/characters/${characterId}/inventory`);\n  }\n\n  async getCharacterStats(characterId: string) {\n    return this.request(`/characters/${characterId}/stats`);\n  }\n\n  async equipItem(characterId: string, inventoryItemId: string) {\n    return this.request(`/characters/${characterId}/equip`, {\n      method: 'POST',\n      body: JSON.stringify({ inventory_item_id: inventoryItemId }),\n    });\n  }\n\n  async useItem(characterId: string, inventoryItemId: string) {\n    return this.request(`/characters/${characterId}/use-item`, {\n      method: 'POST',\n      body: JSON.stringify({ inventory_item_id: inventoryItemId }),\n    });\n  }\n\n  // Game session endpoints\n  async startGameSession(characterId: string, dungeonId: string) {\n    return this.request('/game/start', {\n      method: 'POST',\n      body: JSON.stringify({ character_id: characterId, dungeon_id: dungeonId }),\n    });\n  }\n\n  async getGameSession(sessionId: string) {\n    return this.request(`/game/session/${sessionId}`);\n  }\n\n  async moveToRoom(sessionId: string, roomId: number) {\n    return this.request(`/game/session/${sessionId}/move`, {\n      method: 'POST',\n      body: JSON.stringify({ room_id: roomId }),\n    });\n  }\n\n  async endGameSession(sessionId: string) {\n    return this.request(`/game/session/${sessionId}/end`, {\n      method: 'POST',\n    });\n  }\n\n  // Combat endpoints\n  async startCombat(sessionId: string, enemyId: string) {\n    return this.request('/combat/start', {\n      method: 'POST',\n      body: JSON.stringify({ session_id: sessionId, enemy_id: enemyId }),\n    });\n  }\n\n  async getCombatSession(combatId: string) {\n    return this.request(`/combat/${combatId}`);\n  }\n\n  async performCombatAction(combatId: string, action: any) {\n    return this.request(`/combat/${combatId}/action`, {\n      method: 'POST',\n      body: JSON.stringify(action),\n    });\n  }\n\n  // Dungeon endpoints\n  async getDungeons() {\n    return this.request('/dungeons');\n  }\n\n  async getDungeon(dungeonId: string) {\n    return this.request(`/dungeons/${dungeonId}`);\n  }\n\n  // Items endpoints\n  async getItems() {\n    return this.request('/items');\n  }\n\n  async getItem(itemId: string) {\n    return this.request(`/items/${itemId}`);\n  }\n\n  // Admin endpoints\n  async initializeDatabase() {\n    return this.request('/init-db', {\n      method: 'POST',\n    });\n  }\n\n  async seedDatabase() {\n    return this.request('/seed-data', {\n      method: 'POST',\n    });\n  }\n}\n\n// Create singleton instance\nexport const apiClient = new ApiClient();\n\n// Hook for React components\nexport function useApi() {\n  return apiClient;\n}\n\n// Utility functions for common patterns\nexport async function withOptimisticUpdate<T>(\n  optimisticUpdate: () => void,\n  serverUpdate: () => Promise<T>,\n  revertUpdate: () => void\n): Promise<T> {\n  // Apply optimistic update immediately\n  optimisticUpdate();\n\n  try {\n    // Perform server update\n    const result = await serverUpdate();\n    return result;\n  } catch (error) {\n    // Revert optimistic update on failure\n    revertUpdate();\n    throw error;\n  }\n}\n\n// Retry mechanism for failed requests\nexport async function withRetry<T>(\n  operation: () => Promise<T>,\n  maxRetries: number = 3,\n  delay: number = 1000\n): Promise<T> {\n  let lastError: Error;\n\n  for (let attempt = 1; attempt <= maxRetries; attempt++) {\n    try {\n      return await operation();\n    } catch (error) {\n      lastError = error as Error;\n      \n      if (attempt === maxRetries) {\n        throw lastError;\n      }\n\n      // Wait before retrying\n      await new Promise(resolve => setTimeout(resolve, delay * attempt));\n    }\n  }\n\n  throw lastError!;\n}\n\n// Debounce utility for frequent updates\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Cache for API responses\nclass ApiCache {\n  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();\n\n  set(key: string, data: any, ttl: number = 5 * 60 * 1000) { // 5 minutes default\n    this.cache.set(key, {\n      data,\n      timestamp: Date.now(),\n      ttl\n    });\n  }\n\n  get(key: string): any | null {\n    const entry = this.cache.get(key);\n    \n    if (!entry) {\n      return null;\n    }\n\n    if (Date.now() - entry.timestamp > entry.ttl) {\n      this.cache.delete(key);\n      return null;\n    }\n\n    return entry.data;\n  }\n\n  clear() {\n    this.cache.clear();\n  }\n\n  delete(key: string) {\n    this.cache.delete(key);\n  }\n}\n\nexport const apiCache = new ApiCache();\n\n// Cached API request wrapper\nexport async function cachedRequest<T>(\n  key: string,\n  requestFn: () => Promise<T>,\n  ttl?: number\n): Promise<T> {\n  // Check cache first\n  const cached = apiCache.get(key);\n  if (cached) {\n    return cached;\n  }\n\n  // Make request and cache result\n  const result = await requestFn();\n  apiCache.set(key, result, ttl);\n  \n  return result;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA,MAAM;IACI,QAAgB;IAChB,QAAuB,KAAK;IAEpC,YAAY,UAAkB,MAAM,CAAE;QACpC,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,SAAS,KAAoB,EAAE;QAC7B,IAAI,CAAC,KAAK,GAAG;IACf;IAEQ,aAA0B;QAChC,MAAM,UAAuB;YAC3B,gBAAgB;QAClB;QAEA,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE;QACnD;QAEA,OAAO;IACT;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,MAAM,SAAsB;YAC1B,GAAG,OAAO;YACV,SAAS;gBACP,GAAG,IAAI,CAAC,UAAU,EAAE;gBACpB,GAAG,QAAQ,OAAO;YACpB;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YACxE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,UAAU,EAAE;YACjD,MAAM;QACR;IACF;IAEA,iBAAiB;IACjB,MAAM,SAAS,KAAa,EAAE,QAAgB,EAAE,QAAgB,EAAE;QAChE,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB;YACpC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAO;gBAAU;YAAS;QACnD;IACF;IAEA,MAAM,MAAM,KAAa,EAAE,QAAgB,EAAE;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe;YACjC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAO;YAAS;QACzC;IACF;IAEA,sBAAsB;IACtB,MAAM,gBAAgB;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,MAAM,aAAa,WAAmB,EAAE;QACtC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,aAAa;IAClD;IAEA,MAAM,gBAAgB,IAAY,EAAE;QAClC,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe;YACjC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAK;QAC9B;IACF;IAEA,MAAM,sBAAsB,WAAmB,EAAE;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,YAAY,UAAU,CAAC;IAC5D;IAEA,MAAM,kBAAkB,WAAmB,EAAE;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,YAAY,MAAM,CAAC;IACxD;IAEA,MAAM,UAAU,WAAmB,EAAE,eAAuB,EAAE;QAC5D,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,YAAY,MAAM,CAAC,EAAE;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE,mBAAmB;YAAgB;QAC5D;IACF;IAEA,MAAM,QAAQ,WAAmB,EAAE,eAAuB,EAAE;QAC1D,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,YAAY,SAAS,CAAC,EAAE;YACzD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE,mBAAmB;YAAgB;QAC5D;IACF;IAEA,yBAAyB;IACzB,MAAM,iBAAiB,WAAmB,EAAE,SAAiB,EAAE;QAC7D,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe;YACjC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE,cAAc;gBAAa,YAAY;YAAU;QAC1E;IACF;IAEA,MAAM,eAAe,SAAiB,EAAE;QACtC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,WAAW;IAClD;IAEA,MAAM,WAAW,SAAiB,EAAE,MAAc,EAAE;QAClD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,UAAU,KAAK,CAAC,EAAE;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE,SAAS;YAAO;QACzC;IACF;IAEA,MAAM,eAAe,SAAiB,EAAE;QACtC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,UAAU,IAAI,CAAC,EAAE;YACpD,QAAQ;QACV;IACF;IAEA,mBAAmB;IACnB,MAAM,YAAY,SAAiB,EAAE,OAAe,EAAE;QACpD,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE,YAAY;gBAAW,UAAU;YAAQ;QAClE;IACF;IAEA,MAAM,iBAAiB,QAAgB,EAAE;QACvC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,UAAU;IAC3C;IAEA,MAAM,oBAAoB,QAAgB,EAAE,MAAW,EAAE;QACvD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,SAAS,OAAO,CAAC,EAAE;YAChD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,oBAAoB;IACpB,MAAM,cAAc;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,MAAM,WAAW,SAAiB,EAAE;QAClC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,WAAW;IAC9C;IAEA,kBAAkB;IAClB,MAAM,WAAW;QACf,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,MAAM,QAAQ,MAAc,EAAE;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,QAAQ;IACxC;IAEA,kBAAkB;IAClB,MAAM,qBAAqB;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY;YAC9B,QAAQ;QACV;IACF;IAEA,MAAM,eAAe;QACnB,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc;YAChC,QAAQ;QACV;IACF;AACF;AAGO,MAAM,YAAY,IAAI;AAGtB,SAAS;IACd,OAAO;AACT;AAGO,eAAe,qBACpB,gBAA4B,EAC5B,YAA8B,EAC9B,YAAwB;IAExB,sCAAsC;IACtC;IAEA,IAAI;QACF,wBAAwB;QACxB,MAAM,SAAS,MAAM;QACrB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,sCAAsC;QACtC;QACA,MAAM;IACR;AACF;AAGO,eAAe,UACpB,SAA2B,EAC3B,aAAqB,CAAC,EACtB,QAAgB,IAAI;IAEpB,IAAI;IAEJ,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;QACtD,IAAI;YACF,OAAO,MAAM;QACf,EAAE,OAAO,OAAO;YACd,YAAY;YAEZ,IAAI,YAAY,YAAY;gBAC1B,MAAM;YACR;YAEA,uBAAuB;YACvB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ;QAC3D;IACF;IAEA,MAAM;AACR;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEA,0BAA0B;AAC1B,MAAM;IACI,QAAQ,IAAI,MAA6D;IAEjF,IAAI,GAAW,EAAE,IAAS,EAAE,MAAc,IAAI,KAAK,IAAI,EAAE;QACvD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;YAClB;YACA,WAAW,KAAK,GAAG;YACnB;QACF;IACF;IAEA,IAAI,GAAW,EAAc;QAC3B,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAE7B,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,IAAI,KAAK,GAAG,KAAK,MAAM,SAAS,GAAG,MAAM,GAAG,EAAE;YAC5C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,OAAO,MAAM,IAAI;IACnB;IAEA,QAAQ;QACN,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA,OAAO,GAAW,EAAE;QAClB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IACpB;AACF;AAEO,MAAM,WAAW,IAAI;AAGrB,eAAe,cACpB,GAAW,EACX,SAA2B,EAC3B,GAAY;IAEZ,oBAAoB;IACpB,MAAM,SAAS,SAAS,GAAG,CAAC;IAC5B,IAAI,QAAQ;QACV,OAAO;IACT;IAEA,gCAAgC;IAChC,MAAM,SAAS,MAAM;IACrB,SAAS,GAAG,CAAC,KAAK,QAAQ;IAE1B,OAAO;AACT", "debugId": null}}, {"offset": {"line": 556, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/projects/blackbird/src/hooks/useGameActions.ts"], "sourcesContent": ["import { useCallback } from 'react';\nimport { useGameStore, use<PERSON>uth, useCharacter, useCombat, useUI } from '../store/gameStore';\nimport { apiClient, withOptimisticUpdate } from '../lib/api';\nimport { Character, CreateCharacterRequest, LoginRequest, CreateUserRequest } from '../types/game';\n\nexport function useAuthActions() {\n  const { setAuth, logout, setAuthLoading, addNotification } = useGameStore();\n\n  const login = useCallback(async (credentials: LoginRequest) => {\n    setAuthLoading(true);\n    try {\n      const response = await apiClient.login(credentials.email, credentials.password);\n      \n      if (response.success && response.data) {\n        const { user, token } = response.data;\n        apiClient.setToken(token);\n        setAuth(user, token);\n        addNotification({\n          type: 'success',\n          message: 'Login successful!'\n        });\n        return { success: true };\n      } else {\n        throw new Error(response.error || 'Login failed');\n      }\n    } catch (error) {\n      const message = error instanceof Error ? error.message : 'Login failed';\n      addNotification({\n        type: 'error',\n        message\n      });\n      return { success: false, error: message };\n    } finally {\n      setAuthLoading(false);\n    }\n  }, [setAuth, setAuthLoading, addNotification]);\n\n  const register = useCallback(async (userData: CreateUserRequest) => {\n    setAuthLoading(true);\n    try {\n      const response = await apiClient.register(userData.email, userData.username, userData.password);\n      \n      if (response.success) {\n        addNotification({\n          type: 'success',\n          message: 'Registration successful! Please log in.'\n        });\n        return { success: true };\n      } else {\n        throw new Error(response.error || 'Registration failed');\n      }\n    } catch (error) {\n      const message = error instanceof Error ? error.message : 'Registration failed';\n      addNotification({\n        type: 'error',\n        message\n      });\n      return { success: false, error: message };\n    } finally {\n      setAuthLoading(false);\n    }\n  }, [setAuthLoading, addNotification]);\n\n  const handleLogout = useCallback(() => {\n    apiClient.setToken(null);\n    logout();\n    addNotification({\n      type: 'info',\n      message: 'Logged out successfully'\n    });\n  }, [logout, addNotification]);\n\n  return {\n    login,\n    register,\n    logout: handleLogout\n  };\n}\n\nexport function useCharacterActions() {\n  const { \n    setCharacters, \n    setCurrentCharacter, \n    updateCharacter, \n    setCharacterInventory,\n    updateInventoryItem,\n    removeInventoryItem,\n    optimisticCharacterUpdate,\n    revertOptimisticUpdate,\n    addNotification\n  } = useGameStore();\n\n  const loadCharacters = useCallback(async () => {\n    try {\n      const response = await apiClient.getCharacters();\n      if (response.success && response.data) {\n        setCharacters(response.data);\n      }\n    } catch (error) {\n      addNotification({\n        type: 'error',\n        message: 'Failed to load characters'\n      });\n    }\n  }, [setCharacters, addNotification]);\n\n  const createCharacter = useCallback(async (characterData: CreateCharacterRequest) => {\n    try {\n      const response = await apiClient.createCharacter(characterData.name);\n      \n      if (response.success && response.data) {\n        const newCharacter = response.data;\n        setCharacters(await apiClient.getCharacters().then(r => r.data || []));\n        addNotification({\n          type: 'success',\n          message: `Character \"${newCharacter.name}\" created successfully!`\n        });\n        return { success: true, character: newCharacter };\n      } else {\n        throw new Error(response.error || 'Failed to create character');\n      }\n    } catch (error) {\n      const message = error instanceof Error ? error.message : 'Failed to create character';\n      addNotification({\n        type: 'error',\n        message\n      });\n      return { success: false, error: message };\n    }\n  }, [setCharacters, addNotification]);\n\n  const selectCharacter = useCallback(async (character: Character) => {\n    setCurrentCharacter(character);\n    \n    // Load character inventory\n    try {\n      const response = await apiClient.getCharacterInventory(character.id);\n      if (response.success && response.data) {\n        setCharacterInventory(response.data);\n      }\n    } catch (error) {\n      addNotification({\n        type: 'error',\n        message: 'Failed to load character inventory'\n      });\n    }\n  }, [setCurrentCharacter, setCharacterInventory, addNotification]);\n\n  const useItem = useCallback(async (characterId: string, inventoryItemId: string) => {\n    try {\n      const response = await apiClient.useItem(characterId, inventoryItemId);\n      \n      if (response.success && response.data) {\n        const { character, itemUsed } = response.data;\n        updateCharacter(character);\n        \n        if (itemUsed) {\n          // Refresh inventory\n          const inventoryResponse = await apiClient.getCharacterInventory(characterId);\n          if (inventoryResponse.success && inventoryResponse.data) {\n            setCharacterInventory(inventoryResponse.data);\n          }\n        }\n\n        addNotification({\n          type: 'success',\n          message: 'Item used successfully!'\n        });\n        return { success: true };\n      } else {\n        throw new Error(response.error || 'Failed to use item');\n      }\n    } catch (error) {\n      const message = error instanceof Error ? error.message : 'Failed to use item';\n      addNotification({\n        type: 'error',\n        message\n      });\n      return { success: false, error: message };\n    }\n  }, [updateCharacter, setCharacterInventory, addNotification]);\n\n  const equipItem = useCallback(async (characterId: string, inventoryItemId: string) => {\n    return withOptimisticUpdate(\n      // Optimistic update\n      () => {\n        // This would update the UI immediately\n        // Implementation depends on your specific UI needs\n      },\n      // Server update\n      async () => {\n        const response = await apiClient.equipItem(characterId, inventoryItemId);\n        \n        if (response.success && response.data) {\n          updateInventoryItem(response.data);\n          addNotification({\n            type: 'success',\n            message: 'Item equipped successfully!'\n          });\n          return response.data;\n        } else {\n          throw new Error(response.error || 'Failed to equip item');\n        }\n      },\n      // Revert function\n      () => {\n        revertOptimisticUpdate();\n      }\n    );\n  }, [updateInventoryItem, revertOptimisticUpdate, addNotification]);\n\n  return {\n    loadCharacters,\n    createCharacter,\n    selectCharacter,\n    useItem,\n    equipItem\n  };\n}\n\nexport function useGameSessionActions() {\n  const { \n    setCurrentSession, \n    setCurrentDungeon, \n    setCurrentFloor,\n    setGameState,\n    addNotification \n  } = useGameStore();\n\n  const startGameSession = useCallback(async (characterId: string, dungeonId: string) => {\n    try {\n      const response = await apiClient.startGameSession(characterId, dungeonId);\n      \n      if (response.success && response.data) {\n        const session = response.data;\n        setCurrentSession(session);\n        addNotification({\n          type: 'success',\n          message: 'Game session started!'\n        });\n        return { success: true, session };\n      } else {\n        throw new Error(response.error || 'Failed to start game session');\n      }\n    } catch (error) {\n      const message = error instanceof Error ? error.message : 'Failed to start game session';\n      addNotification({\n        type: 'error',\n        message\n      });\n      return { success: false, error: message };\n    }\n  }, [setCurrentSession, addNotification]);\n\n  const endGameSession = useCallback(async (sessionId: string) => {\n    try {\n      const response = await apiClient.endGameSession(sessionId);\n      \n      if (response.success) {\n        setCurrentSession(null);\n        setCurrentDungeon(null);\n        setCurrentFloor(null);\n        setGameState(null);\n        addNotification({\n          type: 'info',\n          message: 'Game session ended'\n        });\n        return { success: true };\n      } else {\n        throw new Error(response.error || 'Failed to end game session');\n      }\n    } catch (error) {\n      const message = error instanceof Error ? error.message : 'Failed to end game session';\n      addNotification({\n        type: 'error',\n        message\n      });\n      return { success: false, error: message };\n    }\n  }, [setCurrentSession, setCurrentDungeon, setCurrentFloor, setGameState, addNotification]);\n\n  return {\n    startGameSession,\n    endGameSession\n  };\n}\n\nexport function useCombatActions() {\n  const { \n    setCurrentCombat, \n    setCombatLoading, \n    setLastAction,\n    updateCharacter,\n    addNotification \n  } = useGameStore();\n\n  const startCombat = useCallback(async (sessionId: string, enemyId: string) => {\n    setCombatLoading(true);\n    try {\n      const response = await apiClient.startCombat(sessionId, enemyId);\n      \n      if (response.success && response.data) {\n        const combat = response.data;\n        setCurrentCombat(combat);\n        addNotification({\n          type: 'info',\n          message: 'Combat started!'\n        });\n        return { success: true, combat };\n      } else {\n        throw new Error(response.error || 'Failed to start combat');\n      }\n    } catch (error) {\n      const message = error instanceof Error ? error.message : 'Failed to start combat';\n      addNotification({\n        type: 'error',\n        message\n      });\n      return { success: false, error: message };\n    } finally {\n      setCombatLoading(false);\n    }\n  }, [setCurrentCombat, setCombatLoading, addNotification]);\n\n  const performAction = useCallback(async (combatId: string, action: any) => {\n    setCombatLoading(true);\n    try {\n      const response = await apiClient.performCombatAction(combatId, action);\n      \n      if (response.success && response.data) {\n        const { combat, character } = response.data;\n        setCurrentCombat(combat);\n        setLastAction(action);\n        \n        if (character) {\n          updateCharacter(character);\n        }\n\n        // Check if combat ended\n        if (!combat.is_active) {\n          const winner = combat.winner;\n          addNotification({\n            type: winner === 'character' ? 'success' : 'error',\n            message: winner === 'character' ? 'Victory!' : 'Defeat!'\n          });\n        }\n\n        return { success: true, combat };\n      } else {\n        throw new Error(response.error || 'Failed to perform action');\n      }\n    } catch (error) {\n      const message = error instanceof Error ? error.message : 'Failed to perform action';\n      addNotification({\n        type: 'error',\n        message\n      });\n      return { success: false, error: message };\n    } finally {\n      setCombatLoading(false);\n    }\n  }, [setCurrentCombat, setLastAction, updateCharacter, setCombatLoading, addNotification]);\n\n  return {\n    startCombat,\n    performAction\n  };\n}\n\nexport function useNotifications() {\n  const { notifications, removeNotification, clearNotifications } = useUI();\n\n  const dismissNotification = useCallback((id: string) => {\n    removeNotification(id);\n  }, [removeNotification]);\n\n  const clearAll = useCallback(() => {\n    clearNotifications();\n  }, [clearNotifications]);\n\n  return {\n    notifications,\n    dismissNotification,\n    clearAll\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;;AAGO,SAAS;;IACd,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAExE,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE,OAAO;YAC/B,eAAe;YACf,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,YAAY,KAAK,EAAE,YAAY,QAAQ;gBAE9E,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI;oBACrC,oHAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;oBACnB,QAAQ,MAAM;oBACd,gBAAgB;wBACd,MAAM;wBACN,SAAS;oBACX;oBACA,OAAO;wBAAE,SAAS;oBAAK;gBACzB,OAAO;oBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACzD,gBAAgB;oBACd,MAAM;oBACN;gBACF;gBACA,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAQ;YAC1C,SAAU;gBACR,eAAe;YACjB;QACF;4CAAG;QAAC;QAAS;QAAgB;KAAgB;IAE7C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,OAAO;YAClC,eAAe;YACf,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,SAAS,KAAK,EAAE,SAAS,QAAQ,EAAE,SAAS,QAAQ;gBAE9F,IAAI,SAAS,OAAO,EAAE;oBACpB,gBAAgB;wBACd,MAAM;wBACN,SAAS;oBACX;oBACA,OAAO;wBAAE,SAAS;oBAAK;gBACzB,OAAO;oBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACzD,gBAAgB;oBACd,MAAM;oBACN;gBACF;gBACA,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAQ;YAC1C,SAAU;gBACR,eAAe;YACjB;QACF;+CAAG;QAAC;QAAgB;KAAgB;IAEpC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YAC/B,oHAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YACnB;YACA,gBAAgB;gBACd,MAAM;gBACN,SAAS;YACX;QACF;mDAAG;QAAC;QAAQ;KAAgB;IAE5B,OAAO;QACL;QACA;QACA,QAAQ;IACV;AACF;GAxEgB;;QAC+C,4HAAA,CAAA,eAAY;;;AAyEpE,SAAS;;IACd,MAAM,EACJ,aAAa,EACb,mBAAmB,EACnB,eAAe,EACf,qBAAqB,EACrB,mBAAmB,EACnB,mBAAmB,EACnB,yBAAyB,EACzB,sBAAsB,EACtB,eAAe,EAChB,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEf,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YACjC,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,aAAa;gBAC9C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,cAAc,SAAS,IAAI;gBAC7B;YACF,EAAE,OAAO,OAAO;gBACd,gBAAgB;oBACd,MAAM;oBACN,SAAS;gBACX;YACF;QACF;0DAAG;QAAC;QAAe;KAAgB;IAEnC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,OAAO;YACzC,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,eAAe,CAAC,cAAc,IAAI;gBAEnE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,MAAM,eAAe,SAAS,IAAI;oBAClC,cAAc,MAAM,oHAAA,CAAA,YAAS,CAAC,aAAa,GAAG,IAAI;4EAAC,CAAA,IAAK,EAAE,IAAI,IAAI,EAAE;;oBACpE,gBAAgB;wBACd,MAAM;wBACN,SAAS,CAAC,WAAW,EAAE,aAAa,IAAI,CAAC,uBAAuB,CAAC;oBACnE;oBACA,OAAO;wBAAE,SAAS;wBAAM,WAAW;oBAAa;gBAClD,OAAO;oBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACzD,gBAAgB;oBACd,MAAM;oBACN;gBACF;gBACA,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAQ;YAC1C;QACF;2DAAG;QAAC;QAAe;KAAgB;IAEnC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,OAAO;YACzC,oBAAoB;YAEpB,2BAA2B;YAC3B,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,qBAAqB,CAAC,UAAU,EAAE;gBACnE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,sBAAsB,SAAS,IAAI;gBACrC;YACF,EAAE,OAAO,OAAO;gBACd,gBAAgB;oBACd,MAAM;oBACN,SAAS;gBACX;YACF;QACF;2DAAG;QAAC;QAAqB;QAAuB;KAAgB;IAEhE,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,OAAO,aAAqB;YACtD,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,OAAO,CAAC,aAAa;gBAEtD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,SAAS,IAAI;oBAC7C,gBAAgB;oBAEhB,IAAI,UAAU;wBACZ,oBAAoB;wBACpB,MAAM,oBAAoB,MAAM,oHAAA,CAAA,YAAS,CAAC,qBAAqB,CAAC;wBAChE,IAAI,kBAAkB,OAAO,IAAI,kBAAkB,IAAI,EAAE;4BACvD,sBAAsB,kBAAkB,IAAI;wBAC9C;oBACF;oBAEA,gBAAgB;wBACd,MAAM;wBACN,SAAS;oBACX;oBACA,OAAO;wBAAE,SAAS;oBAAK;gBACzB,OAAO;oBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACzD,gBAAgB;oBACd,MAAM;oBACN;gBACF;gBACA,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAQ;YAC1C;QACF;mDAAG;QAAC;QAAiB;QAAuB;KAAgB;IAE5D,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,OAAO,aAAqB;YACxD,OAAO,CAAA,GAAA,oHAAA,CAAA,uBAAoB,AAAD;8DAExB,AADA,oBAAoB;gBACpB;gBACE,uCAAuC;gBACvC,mDAAmD;gBACrD;;8DAEA,AADA,gBAAgB;gBAChB;oBACE,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,SAAS,CAAC,aAAa;oBAExD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;wBACrC,oBAAoB,SAAS,IAAI;wBACjC,gBAAgB;4BACd,MAAM;4BACN,SAAS;wBACX;wBACA,OAAO,SAAS,IAAI;oBACtB,OAAO;wBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;oBACpC;gBACF;;8DAEA,AADA,kBAAkB;gBAClB;oBACE;gBACF;;QAEJ;qDAAG;QAAC;QAAqB;QAAwB;KAAgB;IAEjE,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;IA3IgB;;QAWV,4HAAA,CAAA,eAAY;;;AAkIX,SAAS;;IACd,MAAM,EACJ,iBAAiB,EACjB,iBAAiB,EACjB,eAAe,EACf,YAAY,EACZ,eAAe,EAChB,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEf,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE,OAAO,aAAqB;YAC/D,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,aAAa;gBAE/D,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,MAAM,UAAU,SAAS,IAAI;oBAC7B,kBAAkB;oBAClB,gBAAgB;wBACd,MAAM;wBACN,SAAS;oBACX;oBACA,OAAO;wBAAE,SAAS;wBAAM;oBAAQ;gBAClC,OAAO;oBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACzD,gBAAgB;oBACd,MAAM;oBACN;gBACF;gBACA,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAQ;YAC1C;QACF;8DAAG;QAAC;QAAmB;KAAgB;IAEvC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,OAAO;YACxC,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,cAAc,CAAC;gBAEhD,IAAI,SAAS,OAAO,EAAE;oBACpB,kBAAkB;oBAClB,kBAAkB;oBAClB,gBAAgB;oBAChB,aAAa;oBACb,gBAAgB;wBACd,MAAM;wBACN,SAAS;oBACX;oBACA,OAAO;wBAAE,SAAS;oBAAK;gBACzB,OAAO;oBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACzD,gBAAgB;oBACd,MAAM;oBACN;gBACF;gBACA,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAQ;YAC1C;QACF;4DAAG;QAAC;QAAmB;QAAmB;QAAiB;QAAc;KAAgB;IAEzF,OAAO;QACL;QACA;IACF;AACF;IAjEgB;;QAOV,4HAAA,CAAA,eAAY;;;AA4DX,SAAS;;IACd,MAAM,EACJ,gBAAgB,EAChB,gBAAgB,EAChB,aAAa,EACb,eAAe,EACf,eAAe,EAChB,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEf,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,OAAO,WAAmB;YACxD,iBAAiB;YACjB,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,WAAW,CAAC,WAAW;gBAExD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,MAAM,SAAS,SAAS,IAAI;oBAC5B,iBAAiB;oBACjB,gBAAgB;wBACd,MAAM;wBACN,SAAS;oBACX;oBACA,OAAO;wBAAE,SAAS;wBAAM;oBAAO;gBACjC,OAAO;oBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACzD,gBAAgB;oBACd,MAAM;oBACN;gBACF;gBACA,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAQ;YAC1C,SAAU;gBACR,iBAAiB;YACnB;QACF;oDAAG;QAAC;QAAkB;QAAkB;KAAgB;IAExD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,OAAO,UAAkB;YACzD,iBAAiB;YACjB,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,mBAAmB,CAAC,UAAU;gBAE/D,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,IAAI;oBAC3C,iBAAiB;oBACjB,cAAc;oBAEd,IAAI,WAAW;wBACb,gBAAgB;oBAClB;oBAEA,wBAAwB;oBACxB,IAAI,CAAC,OAAO,SAAS,EAAE;wBACrB,MAAM,SAAS,OAAO,MAAM;wBAC5B,gBAAgB;4BACd,MAAM,WAAW,cAAc,YAAY;4BAC3C,SAAS,WAAW,cAAc,aAAa;wBACjD;oBACF;oBAEA,OAAO;wBAAE,SAAS;wBAAM;oBAAO;gBACjC,OAAO;oBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACzD,gBAAgB;oBACd,MAAM;oBACN;gBACF;gBACA,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAQ;YAC1C,SAAU;gBACR,iBAAiB;YACnB;QACF;sDAAG;QAAC;QAAkB;QAAe;QAAiB;QAAkB;KAAgB;IAExF,OAAO;QACL;QACA;IACF;AACF;IAhFgB;;QAOV,4HAAA,CAAA,eAAY;;;AA2EX,SAAS;;IACd,MAAM,EAAE,aAAa,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,QAAK,AAAD;IAEtE,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAAC;YACvC,mBAAmB;QACrB;4DAAG;QAAC;KAAmB;IAEvB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC3B;QACF;iDAAG;QAAC;KAAmB;IAEvB,OAAO;QACL;QACA;QACA;IACF;AACF;IAhBgB;;QACoD,4HAAA,CAAA,QAAK", "debugId": null}}, {"offset": {"line": 1057, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/projects/blackbird/src/components/AuthScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useAuthActions } from '../hooks/useGameActions';\n\nexport default function AuthScreen() {\n  const [isLogin, setIsLogin] = useState(true);\n  const [formData, setFormData] = useState({\n    email: '',\n    username: '',\n    password: ''\n  });\n  const [isLoading, setIsLoading] = useState(false);\n\n  const { login, register } = useAuthActions();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n\n    try {\n      if (isLogin) {\n        await login({ email: formData.email, password: formData.password });\n      } else {\n        const result = await register({\n          email: formData.email,\n          username: formData.username,\n          password: formData.password\n        });\n        \n        if (result.success) {\n          setIsLogin(true);\n          setFormData({ ...formData, password: '' });\n        }\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4\">\n      <div className=\"bg-black/20 backdrop-blur-sm border border-white/10 rounded-lg p-8 w-full max-w-md\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-white mb-2\">Blackbird</h1>\n          <p className=\"text-gray-300\">Turn-based Dungeon Crawler</p>\n        </div>\n\n        <div className=\"flex mb-6\">\n          <button\n            type=\"button\"\n            onClick={() => setIsLogin(true)}\n            className={`flex-1 py-2 px-4 rounded-l-lg font-medium transition-colors ${\n              isLogin\n                ? 'bg-blue-600 text-white'\n                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'\n            }`}\n          >\n            Login\n          </button>\n          <button\n            type=\"button\"\n            onClick={() => setIsLogin(false)}\n            className={`flex-1 py-2 px-4 rounded-r-lg font-medium transition-colors ${\n              !isLogin\n                ? 'bg-blue-600 text-white'\n                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'\n            }`}\n          >\n            Register\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div>\n            <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-300 mb-1\">\n              Email\n            </label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleInputChange}\n              required\n              className=\"w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"Enter your email\"\n            />\n          </div>\n\n          {!isLogin && (\n            <div>\n              <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-300 mb-1\">\n                Username\n              </label>\n              <input\n                type=\"text\"\n                id=\"username\"\n                name=\"username\"\n                value={formData.username}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                placeholder=\"Choose a username\"\n              />\n            </div>\n          )}\n\n          <div>\n            <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-300 mb-1\">\n              Password\n            </label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              name=\"password\"\n              value={formData.password}\n              onChange={handleInputChange}\n              required\n              className=\"w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"Enter your password\"\n            />\n          </div>\n\n          <button\n            type=\"submit\"\n            disabled={isLoading}\n            className=\"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-lg transition-colors\"\n          >\n            {isLoading ? 'Loading...' : (isLogin ? 'Login' : 'Register')}\n          </button>\n        </form>\n\n        <div className=\"mt-6 text-center\">\n          <button\n            type=\"button\"\n            onClick={async () => {\n              // Quick demo setup\n              setIsLoading(true);\n              try {\n                // Try to initialize database\n                await fetch('/api/init-db', { method: 'POST' });\n                await fetch('/api/seed-data', { method: 'POST' });\n                \n                // Create demo account\n                const demoResult = await register({\n                  email: '<EMAIL>',\n                  username: 'demo',\n                  password: 'demo123'\n                });\n                \n                if (demoResult.success) {\n                  await login({ email: '<EMAIL>', password: 'demo123' });\n                }\n              } catch (error) {\n                console.error('Demo setup failed:', error);\n              } finally {\n                setIsLoading(false);\n              }\n            }}\n            className=\"text-sm text-gray-400 hover:text-gray-300 transition-colors\"\n          >\n            Quick Demo Setup\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;QACV,UAAU;IACZ;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEzC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QAEb,IAAI;YACF,IAAI,SAAS;gBACX,MAAM,MAAM;oBAAE,OAAO,SAAS,KAAK;oBAAE,UAAU,SAAS,QAAQ;gBAAC;YACnE,OAAO;gBACL,MAAM,SAAS,MAAM,SAAS;oBAC5B,OAAO,SAAS,KAAK;oBACrB,UAAU,SAAS,QAAQ;oBAC3B,UAAU,SAAS,QAAQ;gBAC7B;gBAEA,IAAI,OAAO,OAAO,EAAE;oBAClB,WAAW;oBACX,YAAY;wBAAE,GAAG,QAAQ;wBAAE,UAAU;oBAAG;gBAC1C;YACF;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAG/B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,MAAK;4BACL,SAAS,IAAM,WAAW;4BAC1B,WAAW,CAAC,4DAA4D,EACtE,UACI,2BACA,+CACJ;sCACH;;;;;;sCAGD,6LAAC;4BACC,MAAK;4BACL,SAAS,IAAM,WAAW;4BAC1B,WAAW,CAAC,4DAA4D,EACtE,CAAC,UACG,2BACA,+CACJ;sCACH;;;;;;;;;;;;8BAKH,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA+C;;;;;;8CAGhF,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,QAAQ;oCACR,WAAU;oCACV,aAAY;;;;;;;;;;;;wBAIf,CAAC,yBACA,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAA+C;;;;;;8CAGnF,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU;oCACV,QAAQ;oCACR,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKlB,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAA+C;;;;;;8CAGnF,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU;oCACV,QAAQ;oCACR,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,6LAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,YAAY,eAAgB,UAAU,UAAU;;;;;;;;;;;;8BAIrD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,MAAK;wBACL,SAAS;4BACP,mBAAmB;4BACnB,aAAa;4BACb,IAAI;gCACF,6BAA6B;gCAC7B,MAAM,MAAM,gBAAgB;oCAAE,QAAQ;gCAAO;gCAC7C,MAAM,MAAM,kBAAkB;oCAAE,QAAQ;gCAAO;gCAE/C,sBAAsB;gCACtB,MAAM,aAAa,MAAM,SAAS;oCAChC,OAAO;oCACP,UAAU;oCACV,UAAU;gCACZ;gCAEA,IAAI,WAAW,OAAO,EAAE;oCACtB,MAAM,MAAM;wCAAE,OAAO;wCAAuB,UAAU;oCAAU;gCAClE;4BACF,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,sBAAsB;4BACtC,SAAU;gCACR,aAAa;4BACf;wBACF;wBACA,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX;GA1KwB;;QASM,iIAAA,CAAA,iBAAc;;;KATpB", "debugId": null}}, {"offset": {"line": 1357, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/projects/blackbird/src/components/CharacterSelect.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useCharacter } from '../store/gameStore';\nimport { useCharacterActions, useAuthActions } from '../hooks/useGameActions';\n\nexport default function CharacterSelect() {\n  const { characters, isLoading } = useCharacter();\n  const { loadCharacters, createCharacter, select<PERSON>haracter } = useCharacterActions();\n  const { logout } = useAuthActions();\n  \n  const [showCreateForm, setShowCreateForm] = useState(false);\n  const [newCharacterName, setNewCharacterName] = useState('');\n  const [isCreating, setIsCreating] = useState(false);\n\n  useEffect(() => {\n    loadCharacters();\n  }, [loadCharacters]);\n\n  const handleCreateCharacter = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!newCharacterName.trim()) return;\n\n    setIsCreating(true);\n    try {\n      const result = await create<PERSON>haracter({ name: newCharacterName.trim() });\n      if (result.success) {\n        setNewCharacterName('');\n        setShowCreateForm(false);\n      }\n    } finally {\n      setIsCreating(false);\n    }\n  };\n\n  const handleSelectCharacter = (character: any) => {\n    selectCharacter(character);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center\">\n        <div className=\"text-white text-xl\">Loading characters...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4\">\n      <div className=\"max-w-4xl mx-auto\">\n        <div className=\"flex justify-between items-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-white\">Select Character</h1>\n          <button\n            onClick={logout}\n            className=\"px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors\"\n          >\n            Logout\n          </button>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {characters.map((character) => (\n            <div\n              key={character.id}\n              className=\"bg-black/20 backdrop-blur-sm border border-white/10 rounded-lg p-6 hover:bg-black/30 transition-colors cursor-pointer\"\n              onClick={() => handleSelectCharacter(character)}\n            >\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full mx-auto mb-4 flex items-center justify-center\">\n                  <span className=\"text-2xl font-bold text-white\">\n                    {character.name.charAt(0).toUpperCase()}\n                  </span>\n                </div>\n                <h3 className=\"text-xl font-bold text-white mb-2\">{character.name}</h3>\n                <div className=\"text-gray-300 space-y-1\">\n                  <p>Level {character.level}</p>\n                  <p>HP: {character.health}/{character.max_health}</p>\n                  <p>Gold: {character.gold}</p>\n                </div>\n                <div className=\"mt-4 grid grid-cols-2 gap-2 text-sm text-gray-400\">\n                  <div>ATK: {character.attack}</div>\n                  <div>DEF: {character.defense}</div>\n                  <div>SPD: {character.speed}</div>\n                  <div>EXP: {character.experience}</div>\n                </div>\n              </div>\n            </div>\n          ))}\n\n          {/* Create New Character Card */}\n          <div\n            className=\"bg-black/10 backdrop-blur-sm border-2 border-dashed border-white/20 rounded-lg p-6 hover:bg-black/20 transition-colors cursor-pointer flex items-center justify-center\"\n            onClick={() => setShowCreateForm(true)}\n          >\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gray-600 rounded-full mx-auto mb-4 flex items-center justify-center\">\n                <span className=\"text-3xl text-white\">+</span>\n              </div>\n              <p className=\"text-white font-medium\">Create New Character</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Create Character Modal */}\n        {showCreateForm && (\n          <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-gray-900 rounded-lg p-6 w-full max-w-md\">\n              <h2 className=\"text-2xl font-bold text-white mb-4\">Create New Character</h2>\n              \n              <form onSubmit={handleCreateCharacter}>\n                <div className=\"mb-4\">\n                  <label htmlFor=\"characterName\" className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Character Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"characterName\"\n                    value={newCharacterName}\n                    onChange={(e) => setNewCharacterName(e.target.value)}\n                    className=\"w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    placeholder=\"Enter character name\"\n                    maxLength={50}\n                    required\n                  />\n                </div>\n\n                <div className=\"flex gap-3\">\n                  <button\n                    type=\"button\"\n                    onClick={() => {\n                      setShowCreateForm(false);\n                      setNewCharacterName('');\n                    }}\n                    className=\"flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors\"\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    type=\"submit\"\n                    disabled={isCreating || !newCharacterName.trim()}\n                    className=\"flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors\"\n                  >\n                    {isCreating ? 'Creating...' : 'Create'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAC7C,MAAM,EAAE,cAAc,EAAE,eAAe,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,sBAAmB,AAAD;IAC/E,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEhC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG;QAAC;KAAe;IAEnB,MAAM,wBAAwB,OAAO;QACnC,EAAE,cAAc;QAChB,IAAI,CAAC,iBAAiB,IAAI,IAAI;QAE9B,cAAc;QACd,IAAI;YACF,MAAM,SAAS,MAAM,gBAAgB;gBAAE,MAAM,iBAAiB,IAAI;YAAG;YACrE,IAAI,OAAO,OAAO,EAAE;gBAClB,oBAAoB;gBACpB,kBAAkB;YACpB;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,gBAAgB;IAClB;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAqB;;;;;;;;;;;IAG1C;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;8BAKH,6LAAC;oBAAI,WAAU;;wBACZ,WAAW,GAAG,CAAC,CAAC,0BACf,6LAAC;gCAEC,WAAU;gCACV,SAAS,IAAM,sBAAsB;0CAErC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,UAAU,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;sDAGzC,6LAAC;4CAAG,WAAU;sDAAqC,UAAU,IAAI;;;;;;sDACjE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDAAE;wDAAO,UAAU,KAAK;;;;;;;8DACzB,6LAAC;;wDAAE;wDAAK,UAAU,MAAM;wDAAC;wDAAE,UAAU,UAAU;;;;;;;8DAC/C,6LAAC;;wDAAE;wDAAO,UAAU,IAAI;;;;;;;;;;;;;sDAE1B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDAAI;wDAAM,UAAU,MAAM;;;;;;;8DAC3B,6LAAC;;wDAAI;wDAAM,UAAU,OAAO;;;;;;;8DAC5B,6LAAC;;wDAAI;wDAAM,UAAU,KAAK;;;;;;;8DAC1B,6LAAC;;wDAAI;wDAAM,UAAU,UAAU;;;;;;;;;;;;;;;;;;;+BApB9B,UAAU,EAAE;;;;;sCA2BrB,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,kBAAkB;sCAEjC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;kDAExC,6LAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;;;;;;gBAM3C,gCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CAEnD,6LAAC;gCAAK,UAAU;;kDACd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAgB,WAAU;0DAA+C;;;;;;0DAGxF,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDACnD,WAAU;gDACV,aAAY;gDACZ,WAAW;gDACX,QAAQ;;;;;;;;;;;;kDAIZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,SAAS;oDACP,kBAAkB;oDAClB,oBAAoB;gDACtB;gDACA,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,MAAK;gDACL,UAAU,cAAc,CAAC,iBAAiB,IAAI;gDAC9C,WAAU;0DAET,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUlD;GAlJwB;;QACY,4HAAA,CAAA,eAAY;QACe,iIAAA,CAAA,sBAAmB;QAC7D,iIAAA,CAAA,iBAAc;;;KAHX", "debugId": null}}, {"offset": {"line": 1759, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/projects/blackbird/src/components/GameScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { useGameStore, useCharacter, useCombat, useUI } from '../store/gameStore';\nimport { useAuthActions } from '../hooks/useGameActions';\n\nexport default function GameScreen() {\n  const { currentView, setCurrentView } = useGameStore();\n  const { currentCharacter } = useCharacter();\n  const { isInCombat } = useCombat();\n  const { logout } = useAuthActions();\n\n  if (!currentCharacter) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center\">\n        <div className=\"text-white text-xl\">No character selected</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900\">\n      {/* Header */}\n      <div className=\"bg-black/20 backdrop-blur-sm border-b border-white/10 p-4\">\n        <div className=\"max-w-6xl mx-auto flex justify-between items-center\">\n          <div className=\"flex items-center space-x-6\">\n            <h1 className=\"text-2xl font-bold text-white\">{currentCharacter.name}</h1>\n            <div className=\"flex items-center space-x-4 text-sm text-gray-300\">\n              <span>Level {currentCharacter.level}</span>\n              <span>HP: {currentCharacter.health}/{currentCharacter.max_health}</span>\n              <span>Gold: {currentCharacter.gold}</span>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={() => setCurrentView('character-select')}\n              className=\"px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors\"\n            >\n              Change Character\n            </button>\n            <button\n              onClick={logout}\n              className=\"px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors\"\n            >\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div className=\"bg-black/10 backdrop-blur-sm border-b border-white/10 p-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          <nav className=\"flex space-x-4\">\n            <button\n              onClick={() => setCurrentView('dungeon')}\n              className={`px-4 py-2 rounded-lg transition-colors ${\n                currentView === 'dungeon'\n                  ? 'bg-blue-600 text-white'\n                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'\n              }`}\n            >\n              Dungeon\n            </button>\n            <button\n              onClick={() => setCurrentView('inventory')}\n              className={`px-4 py-2 rounded-lg transition-colors ${\n                currentView === 'inventory'\n                  ? 'bg-blue-600 text-white'\n                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'\n              }`}\n            >\n              Inventory\n            </button>\n            {isInCombat && (\n              <button\n                onClick={() => setCurrentView('combat')}\n                className={`px-4 py-2 rounded-lg transition-colors ${\n                  currentView === 'combat'\n                    ? 'bg-red-600 text-white'\n                    : 'bg-red-700 text-red-300 hover:bg-red-600'\n                }`}\n              >\n                Combat!\n              </button>\n            )}\n          </nav>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"max-w-6xl mx-auto p-4\">\n        {currentView === 'dungeon' && <DungeonView />}\n        {currentView === 'inventory' && <InventoryView />}\n        {currentView === 'combat' && <CombatView />}\n      </div>\n    </div>\n  );\n}\n\nfunction DungeonView() {\n  return (\n    <div className=\"bg-black/20 backdrop-blur-sm border border-white/10 rounded-lg p-6\">\n      <h2 className=\"text-2xl font-bold text-white mb-4\">Dungeon Explorer</h2>\n      <div className=\"text-gray-300\">\n        <p className=\"mb-4\">Welcome to the dungeon! This is where you'll explore procedurally generated dungeons, fight enemies, and find treasure.</p>\n        <div className=\"bg-gray-800 rounded-lg p-4\">\n          <p className=\"text-yellow-400 font-medium\">Coming Soon:</p>\n          <ul className=\"mt-2 space-y-1 text-sm\">\n            <li>• Procedural dungeon generation</li>\n            <li>• Room exploration</li>\n            <li>• Enemy encounters</li>\n            <li>• Treasure chests</li>\n            <li>• Boss battles</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nfunction InventoryView() {\n  const { characterInventory } = useCharacter();\n\n  return (\n    <div className=\"bg-black/20 backdrop-blur-sm border border-white/10 rounded-lg p-6\">\n      <h2 className=\"text-2xl font-bold text-white mb-4\">Inventory</h2>\n      \n      {characterInventory.length === 0 ? (\n        <div className=\"text-gray-400 text-center py-8\">\n          <p>Your inventory is empty.</p>\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {characterInventory.map((inventoryItem) => (\n            <div\n              key={inventoryItem.id}\n              className={`bg-gray-800 rounded-lg p-4 border-2 ${\n                inventoryItem.is_equipped\n                  ? 'border-green-500'\n                  : 'border-gray-600'\n              }`}\n            >\n              <div className=\"flex justify-between items-start mb-2\">\n                <h3 className=\"font-bold text-white\">{inventoryItem.item.name}</h3>\n                {inventoryItem.is_equipped && (\n                  <span className=\"text-xs bg-green-600 text-white px-2 py-1 rounded\">\n                    Equipped\n                  </span>\n                )}\n              </div>\n              \n              <p className=\"text-sm text-gray-400 mb-2 capitalize\">\n                {inventoryItem.item.type} • {inventoryItem.item.rarity}\n              </p>\n              \n              {inventoryItem.item.description && (\n                <p className=\"text-sm text-gray-300 mb-3\">\n                  {inventoryItem.item.description}\n                </p>\n              )}\n              \n              <div className=\"text-xs text-gray-400 space-y-1\">\n                {inventoryItem.item.attack_bonus > 0 && (\n                  <div>Attack: +{inventoryItem.item.attack_bonus}</div>\n                )}\n                {inventoryItem.item.defense_bonus > 0 && (\n                  <div>Defense: +{inventoryItem.item.defense_bonus}</div>\n                )}\n                {inventoryItem.item.health_bonus > 0 && (\n                  <div>Health: +{inventoryItem.item.health_bonus}</div>\n                )}\n                {inventoryItem.item.speed_bonus > 0 && (\n                  <div>Speed: +{inventoryItem.item.speed_bonus}</div>\n                )}\n                {inventoryItem.item.heal_amount > 0 && (\n                  <div>Heals: {inventoryItem.item.heal_amount} HP</div>\n                )}\n              </div>\n              \n              {inventoryItem.quantity > 1 && (\n                <div className=\"mt-2 text-sm text-yellow-400\">\n                  Quantity: {inventoryItem.quantity}\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n\nfunction CombatView() {\n  const { currentCombat } = useCombat();\n\n  if (!currentCombat) {\n    return (\n      <div className=\"bg-black/20 backdrop-blur-sm border border-white/10 rounded-lg p-6\">\n        <h2 className=\"text-2xl font-bold text-white mb-4\">Combat</h2>\n        <p className=\"text-gray-300\">No active combat session.</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-black/20 backdrop-blur-sm border border-white/10 rounded-lg p-6\">\n      <h2 className=\"text-2xl font-bold text-white mb-4\">Combat</h2>\n      <div className=\"text-gray-300\">\n        <p className=\"mb-4\">Combat system is ready! You're fighting: {currentCombat.enemy_data.name}</p>\n        <div className=\"bg-gray-800 rounded-lg p-4\">\n          <p className=\"text-red-400 font-medium\">Combat Features:</p>\n          <ul className=\"mt-2 space-y-1 text-sm\">\n            <li>• Turn-based combat</li>\n            <li>• Attack, defend, and use items</li>\n            <li>• Status effects</li>\n            <li>• Experience and gold rewards</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IACnD,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IACxC,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD;IAC/B,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEhC,IAAI,CAAC,kBAAkB;QACrB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAqB;;;;;;;;;;;IAG1C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiC,iBAAiB,IAAI;;;;;;8CACpE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;gDAAK;gDAAO,iBAAiB,KAAK;;;;;;;sDACnC,6LAAC;;gDAAK;gDAAK,iBAAiB,MAAM;gDAAC;gDAAE,iBAAiB,UAAU;;;;;;;sDAChE,6LAAC;;gDAAK;gDAAO,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;;sCAItC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAW,CAAC,uCAAuC,EACjD,gBAAgB,YACZ,2BACA,+CACJ;0CACH;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAW,CAAC,uCAAuC,EACjD,gBAAgB,cACZ,2BACA,+CACJ;0CACH;;;;;;4BAGA,4BACC,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAW,CAAC,uCAAuC,EACjD,gBAAgB,WACZ,0BACA,4CACJ;0CACH;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAI,WAAU;;oBACZ,gBAAgB,2BAAa,6LAAC;;;;;oBAC9B,gBAAgB,6BAAe,6LAAC;;;;;oBAChC,gBAAgB,0BAAY,6LAAC;;;;;;;;;;;;;;;;;AAItC;GA7FwB;;QACkB,4HAAA,CAAA,eAAY;QACvB,4HAAA,CAAA,eAAY;QAClB,4HAAA,CAAA,YAAS;QACb,iIAAA,CAAA,iBAAc;;;KAJX;AA+FxB,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAqC;;;;;;0BACnD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAO;;;;;;kCACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAA8B;;;;;;0CAC3C,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB;MAnBS;AAqBT,SAAS;;IACP,MAAM,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAE1C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAqC;;;;;;YAElD,mBAAmB,MAAM,KAAK,kBAC7B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;8BAAE;;;;;;;;;;qCAGL,6LAAC;gBAAI,WAAU;0BACZ,mBAAmB,GAAG,CAAC,CAAC,8BACvB,6LAAC;wBAEC,WAAW,CAAC,oCAAoC,EAC9C,cAAc,WAAW,GACrB,qBACA,mBACJ;;0CAEF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwB,cAAc,IAAI,CAAC,IAAI;;;;;;oCAC5D,cAAc,WAAW,kBACxB,6LAAC;wCAAK,WAAU;kDAAoD;;;;;;;;;;;;0CAMxE,6LAAC;gCAAE,WAAU;;oCACV,cAAc,IAAI,CAAC,IAAI;oCAAC;oCAAI,cAAc,IAAI,CAAC,MAAM;;;;;;;4BAGvD,cAAc,IAAI,CAAC,WAAW,kBAC7B,6LAAC;gCAAE,WAAU;0CACV,cAAc,IAAI,CAAC,WAAW;;;;;;0CAInC,6LAAC;gCAAI,WAAU;;oCACZ,cAAc,IAAI,CAAC,YAAY,GAAG,mBACjC,6LAAC;;4CAAI;4CAAU,cAAc,IAAI,CAAC,YAAY;;;;;;;oCAE/C,cAAc,IAAI,CAAC,aAAa,GAAG,mBAClC,6LAAC;;4CAAI;4CAAW,cAAc,IAAI,CAAC,aAAa;;;;;;;oCAEjD,cAAc,IAAI,CAAC,YAAY,GAAG,mBACjC,6LAAC;;4CAAI;4CAAU,cAAc,IAAI,CAAC,YAAY;;;;;;;oCAE/C,cAAc,IAAI,CAAC,WAAW,GAAG,mBAChC,6LAAC;;4CAAI;4CAAS,cAAc,IAAI,CAAC,WAAW;;;;;;;oCAE7C,cAAc,IAAI,CAAC,WAAW,GAAG,mBAChC,6LAAC;;4CAAI;4CAAQ,cAAc,IAAI,CAAC,WAAW;4CAAC;;;;;;;;;;;;;4BAI/C,cAAc,QAAQ,GAAG,mBACxB,6LAAC;gCAAI,WAAU;;oCAA+B;oCACjC,cAAc,QAAQ;;;;;;;;uBA9ChC,cAAc,EAAE;;;;;;;;;;;;;;;;AAuDnC;IAtES;;QACwB,4HAAA,CAAA,eAAY;;;MADpC;AAwET,SAAS;;IACP,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD;IAElC,IAAI,CAAC,eAAe;QAClB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAqC;;;;;;8BACnD,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAqC;;;;;;0BACnD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;;4BAAO;4BAA0C,cAAc,UAAU,CAAC,IAAI;;;;;;;kCAC3F,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAA2B;;;;;;0CACxC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB;IA7BS;;QACmB,4HAAA,CAAA,YAAS;;;MAD5B", "debugId": null}}, {"offset": {"line": 2399, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/projects/blackbird/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\nimport { useGameStore } from \"../store/gameStore\";\nimport AuthScreen from \"../components/AuthScreen\";\nimport CharacterSelect from \"../components/CharacterSelect\";\nimport GameScreen from \"../components/GameScreen\";\nimport { apiClient } from \"../lib/api\";\n\nexport default function Home() {\n  const { isAuthenticated, token, currentView, syncWithServer } =\n    useGameStore();\n\n  useEffect(() => {\n    // Set token in API client if user is authenticated\n    if (token) {\n      apiClient.setToken(token);\n    }\n  }, [token]);\n\n  useEffect(() => {\n    // Sync with server periodically if authenticated\n    if (isAuthenticated) {\n      const interval = setInterval(syncWithServer, 30000); // Every 30 seconds\n      return () => clearInterval(interval);\n    }\n  }, [isAuthenticated, syncWithServer]);\n\n  if (!isAuthenticated) {\n    return <AuthScreen />;\n  }\n\n  switch (currentView) {\n    case \"character-select\":\n      return <CharacterSelect />;\n    case \"dungeon\":\n    case \"combat\":\n    case \"inventory\":\n      return <GameScreen />;\n    default:\n      return <CharacterSelect />;\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,KAAK,EAAE,WAAW,EAAE,cAAc,EAAE,GAC3D,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,mDAAmD;YACnD,IAAI,OAAO;gBACT,oHAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YACrB;QACF;yBAAG;QAAC;KAAM;IAEV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,iDAAiD;YACjD,IAAI,iBAAiB;gBACnB,MAAM,WAAW,YAAY,gBAAgB,QAAQ,mBAAmB;gBACxE;sCAAO,IAAM,cAAc;;YAC7B;QACF;yBAAG;QAAC;QAAiB;KAAe;IAEpC,IAAI,CAAC,iBAAiB;QACpB,qBAAO,6LAAC,mIAAA,CAAA,UAAU;;;;;IACpB;IAEA,OAAQ;QACN,KAAK;YACH,qBAAO,6LAAC,wIAAA,CAAA,UAAe;;;;;QACzB,KAAK;QACL,KAAK;QACL,KAAK;YACH,qBAAO,6LAAC,mIAAA,CAAA,UAAU;;;;;QACpB;YACE,qBAAO,6LAAC,wIAAA,CAAA,UAAe;;;;;IAC3B;AACF;GAjCwB;;QAEpB,4HAAA,CAAA,eAAY;;;KAFQ", "debugId": null}}]}