{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/projects/blackbird/src/app/api/auth/login/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\n// Mock user storage (shared with register)\nconst mockUsers: any[] = [\n  {\n    id: 'demo_user',\n    email: '<EMAIL>',\n    username: 'demo',\n    password: 'demo123',\n    created_at: new Date().toISOString(),\n    updated_at: new Date().toISOString(),\n    is_active: true\n  }\n];\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { email, password } = body;\n\n    // Basic validation\n    if (!email || !password) {\n      return NextResponse.json({\n        success: false,\n        error: 'Email and password are required'\n      }, { status: 400 });\n    }\n\n    // Find user\n    const user = mockUsers.find(u => u.email === email && u.password === password);\n    if (!user) {\n      return NextResponse.json({\n        success: false,\n        error: 'Invalid email or password'\n      }, { status: 401 });\n    }\n\n    // Generate mock token\n    const token = `mock_token_${user.id}_${Date.now()}`;\n\n    // Remove password from response\n    const { password: _, ...userWithoutPassword } = user;\n\n    return NextResponse.json({\n      success: true,\n      data: { user: userWithoutPassword, token },\n      message: 'Login successful'\n    });\n\n  } catch (error) {\n    console.error('Login error:', error);\n    \n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Login failed'\n    }, { status: 401 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,2CAA2C;AAC3C,MAAM,YAAmB;IACvB;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,UAAU;QACV,YAAY,IAAI,OAAO,WAAW;QAClC,YAAY,IAAI,OAAO,WAAW;QAClC,WAAW;IACb;CACD;AAEM,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;QAE5B,mBAAmB;QACnB,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,YAAY;QACZ,MAAM,OAAO,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,SAAS,EAAE,QAAQ,KAAK;QACrE,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,sBAAsB;QACtB,MAAM,QAAQ,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;QAEnD,gCAAgC;QAChC,MAAM,EAAE,UAAU,CAAC,EAAE,GAAG,qBAAqB,GAAG;QAEhD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBAAE,MAAM;gBAAqB;YAAM;YACzC,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAE9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}