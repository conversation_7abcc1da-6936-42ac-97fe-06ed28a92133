{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/projects/blackbird/src/app/api/characters/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\n// Mock character storage\nconst mockCharacters: any[] = [];\n\n// Mock inventory items\nconst mockItems = [\n  {\n    id: 'item_1',\n    name: 'Rusty Sword',\n    type: 'weapon',\n    rarity: 'common',\n    attack_bonus: 5,\n    defense_bonus: 0,\n    health_bonus: 0,\n    speed_bonus: 0,\n    heal_amount: 0,\n    description: 'A worn but functional sword.',\n    price: 10\n  },\n  {\n    id: 'item_2',\n    name: 'Leather Vest',\n    type: 'armor',\n    rarity: 'common',\n    attack_bonus: 0,\n    defense_bonus: 3,\n    health_bonus: 5,\n    speed_bonus: 0,\n    heal_amount: 0,\n    description: 'Basic leather protection.',\n    price: 15\n  },\n  {\n    id: 'item_3',\n    name: 'Health Potion',\n    type: 'consumable',\n    rarity: 'common',\n    attack_bonus: 0,\n    defense_bonus: 0,\n    health_bonus: 0,\n    speed_bonus: 0,\n    heal_amount: 30,\n    description: 'Restores 30 health points.',\n    price: 20\n  }\n];\n\nfunction authenticateRequest(request: NextRequest) {\n  const authHeader = request.headers.get('Authorization');\n  if (!authHeader || !authHeader.startsWith('Bearer ')) {\n    return null;\n  }\n  \n  const token = authHeader.substring(7);\n  // Mock authentication - in real app, verify JWT\n  if (token.startsWith('mock_token_')) {\n    return { id: 'demo_user', email: '<EMAIL>' };\n  }\n  \n  return null;\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const user = authenticateRequest(request);\n    if (!user) {\n      return NextResponse.json({\n        success: false,\n        error: 'Authentication required'\n      }, { status: 401 });\n    }\n\n    // Get user's characters\n    const userCharacters = mockCharacters.filter(c => c.user_id === user.id);\n\n    return NextResponse.json({\n      success: true,\n      data: userCharacters\n    });\n\n  } catch (error) {\n    console.error('Characters API error:', error);\n    \n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Internal server error'\n    }, { status: 500 });\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const user = authenticateRequest(request);\n    if (!user) {\n      return NextResponse.json({\n        success: false,\n        error: 'Authentication required'\n      }, { status: 401 });\n    }\n\n    const body = await request.json();\n    const { name } = body;\n\n    if (!name || name.trim().length === 0) {\n      return NextResponse.json({\n        success: false,\n        error: 'Character name is required'\n      }, { status: 400 });\n    }\n\n    // Check if character name already exists for this user\n    const existingCharacter = mockCharacters.find(c => c.user_id === user.id && c.name === name);\n    if (existingCharacter) {\n      return NextResponse.json({\n        success: false,\n        error: 'Character name already exists'\n      }, { status: 400 });\n    }\n\n    // Create new character\n    const newCharacter = {\n      id: `char_${Date.now()}`,\n      user_id: user.id,\n      name: name.trim(),\n      level: 1,\n      experience: 0,\n      health: 100,\n      max_health: 100,\n      attack: 10,\n      defense: 5,\n      speed: 10,\n      gold: 50,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    };\n\n    mockCharacters.push(newCharacter);\n\n    return NextResponse.json({\n      success: true,\n      data: newCharacter,\n      message: 'Character created successfully'\n    }, { status: 201 });\n\n  } catch (error) {\n    console.error('Characters API error:', error);\n    \n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Internal server error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,yBAAyB;AACzB,MAAM,iBAAwB,EAAE;AAEhC,uBAAuB;AACvB,MAAM,YAAY;IAChB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,cAAc;QACd,eAAe;QACf,cAAc;QACd,aAAa;QACb,aAAa;QACb,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,cAAc;QACd,eAAe;QACf,cAAc;QACd,aAAa;QACb,aAAa;QACb,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,cAAc;QACd,eAAe;QACf,cAAc;QACd,aAAa;QACb,aAAa;QACb,aAAa;QACb,OAAO;IACT;CACD;AAED,SAAS,oBAAoB,OAAoB;IAC/C,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;QACpD,OAAO;IACT;IAEA,MAAM,QAAQ,WAAW,SAAS,CAAC;IACnC,gDAAgD;IAChD,IAAI,MAAM,UAAU,CAAC,gBAAgB;QACnC,OAAO;YAAE,IAAI;YAAa,OAAO;QAAsB;IACzD;IAEA,OAAO;AACT;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,OAAO,oBAAoB;QACjC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,wBAAwB;QACxB,MAAM,iBAAiB,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,KAAK,EAAE;QAEvE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,oBAAoB;QACjC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,GAAG;QAEjB,IAAI,CAAC,QAAQ,KAAK,IAAI,GAAG,MAAM,KAAK,GAAG;YACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,uDAAuD;QACvD,MAAM,oBAAoB,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,KAAK;QACvF,IAAI,mBAAmB;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,uBAAuB;QACvB,MAAM,eAAe;YACnB,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;YACxB,SAAS,KAAK,EAAE;YAChB,MAAM,KAAK,IAAI;YACf,OAAO;YACP,YAAY;YACZ,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,SAAS;YACT,OAAO;YACP,MAAM;YACN,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,eAAe,IAAI,CAAC;QAEpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}