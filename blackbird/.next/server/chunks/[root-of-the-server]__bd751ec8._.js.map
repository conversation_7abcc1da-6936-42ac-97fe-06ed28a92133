{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/projects/blackbird/src/app/api/auth/register/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\n// Mock user storage (in production, this would be in a database)\nconst mockUsers: any[] = [];\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { email, username, password } = body;\n\n    // Basic validation\n    if (!email || !username || !password) {\n      return NextResponse.json({\n        success: false,\n        error: 'Email, username, and password are required'\n      }, { status: 400 });\n    }\n\n    // Check if user already exists\n    const existingUser = mockUsers.find(u => u.email === email || u.username === username);\n    if (existingUser) {\n      return NextResponse.json({\n        success: false,\n        error: 'User with this email or username already exists'\n      }, { status: 400 });\n    }\n\n    // Create mock user\n    const newUser = {\n      id: `user_${Date.now()}`,\n      email,\n      username,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n      is_active: true\n    };\n\n    mockUsers.push({ ...newUser, password });\n\n    return NextResponse.json({\n      success: true,\n      data: newUser,\n      message: 'User created successfully'\n    }, { status: 201 });\n\n  } catch (error) {\n    console.error('Registration error:', error);\n    \n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Registration failed'\n    }, { status: 400 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,iEAAiE;AACjE,MAAM,YAAmB,EAAE;AAEpB,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;QAEtC,mBAAmB;QACnB,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,UAAU;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,+BAA+B;QAC/B,MAAM,eAAe,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,SAAS,EAAE,QAAQ,KAAK;QAC7E,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,mBAAmB;QACnB,MAAM,UAAU;YACd,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;YACxB;YACA;YACA,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;YAClC,WAAW;QACb;QAEA,UAAU,IAAI,CAAC;YAAE,GAAG,OAAO;YAAE;QAAS;QAEtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QAErC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}