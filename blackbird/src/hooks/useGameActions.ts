import { useCallback } from 'react';
import { useGameStore, use<PERSON>uth, useCharacter, useCombat, useUI } from '../store/gameStore';
import { apiClient, withOptimisticUpdate } from '../lib/api';
import { Character, CreateCharacterRequest, LoginRequest, CreateUserRequest } from '../types/game';

export function useAuthActions() {
  const { setAuth, logout, setAuthLoading, addNotification } = useGameStore();

  const login = useCallback(async (credentials: LoginRequest) => {
    setAuthLoading(true);
    try {
      const response = await apiClient.login(credentials.email, credentials.password);
      
      if (response.success && response.data) {
        const { user, token } = response.data;
        apiClient.setToken(token);
        setAuth(user, token);
        addNotification({
          type: 'success',
          message: 'Login successful!'
        });
        return { success: true };
      } else {
        throw new Error(response.error || 'Login failed');
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Login failed';
      addNotification({
        type: 'error',
        message
      });
      return { success: false, error: message };
    } finally {
      setAuthLoading(false);
    }
  }, [setAuth, setAuthLoading, addNotification]);

  const register = useCallback(async (userData: CreateUserRequest) => {
    setAuthLoading(true);
    try {
      const response = await apiClient.register(userData.email, userData.username, userData.password);
      
      if (response.success) {
        addNotification({
          type: 'success',
          message: 'Registration successful! Please log in.'
        });
        return { success: true };
      } else {
        throw new Error(response.error || 'Registration failed');
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Registration failed';
      addNotification({
        type: 'error',
        message
      });
      return { success: false, error: message };
    } finally {
      setAuthLoading(false);
    }
  }, [setAuthLoading, addNotification]);

  const handleLogout = useCallback(() => {
    apiClient.setToken(null);
    logout();
    addNotification({
      type: 'info',
      message: 'Logged out successfully'
    });
  }, [logout, addNotification]);

  return {
    login,
    register,
    logout: handleLogout
  };
}

export function useCharacterActions() {
  const { 
    setCharacters, 
    setCurrentCharacter, 
    updateCharacter, 
    setCharacterInventory,
    updateInventoryItem,
    removeInventoryItem,
    optimisticCharacterUpdate,
    revertOptimisticUpdate,
    addNotification
  } = useGameStore();

  const loadCharacters = useCallback(async () => {
    try {
      const response = await apiClient.getCharacters();
      if (response.success && response.data) {
        setCharacters(response.data);
      }
    } catch (error) {
      addNotification({
        type: 'error',
        message: 'Failed to load characters'
      });
    }
  }, [setCharacters, addNotification]);

  const createCharacter = useCallback(async (characterData: CreateCharacterRequest) => {
    try {
      const response = await apiClient.createCharacter(characterData.name);
      
      if (response.success && response.data) {
        const newCharacter = response.data;
        setCharacters(await apiClient.getCharacters().then(r => r.data || []));
        addNotification({
          type: 'success',
          message: `Character "${newCharacter.name}" created successfully!`
        });
        return { success: true, character: newCharacter };
      } else {
        throw new Error(response.error || 'Failed to create character');
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to create character';
      addNotification({
        type: 'error',
        message
      });
      return { success: false, error: message };
    }
  }, [setCharacters, addNotification]);

  const selectCharacter = useCallback(async (character: Character) => {
    setCurrentCharacter(character);
    
    // Load character inventory
    try {
      const response = await apiClient.getCharacterInventory(character.id);
      if (response.success && response.data) {
        setCharacterInventory(response.data);
      }
    } catch (error) {
      addNotification({
        type: 'error',
        message: 'Failed to load character inventory'
      });
    }
  }, [setCurrentCharacter, setCharacterInventory, addNotification]);

  const useItem = useCallback(async (characterId: string, inventoryItemId: string) => {
    try {
      const response = await apiClient.useItem(characterId, inventoryItemId);
      
      if (response.success && response.data) {
        const { character, itemUsed } = response.data;
        updateCharacter(character);
        
        if (itemUsed) {
          // Refresh inventory
          const inventoryResponse = await apiClient.getCharacterInventory(characterId);
          if (inventoryResponse.success && inventoryResponse.data) {
            setCharacterInventory(inventoryResponse.data);
          }
        }

        addNotification({
          type: 'success',
          message: 'Item used successfully!'
        });
        return { success: true };
      } else {
        throw new Error(response.error || 'Failed to use item');
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to use item';
      addNotification({
        type: 'error',
        message
      });
      return { success: false, error: message };
    }
  }, [updateCharacter, setCharacterInventory, addNotification]);

  const equipItem = useCallback(async (characterId: string, inventoryItemId: string) => {
    return withOptimisticUpdate(
      // Optimistic update
      () => {
        // This would update the UI immediately
        // Implementation depends on your specific UI needs
      },
      // Server update
      async () => {
        const response = await apiClient.equipItem(characterId, inventoryItemId);
        
        if (response.success && response.data) {
          updateInventoryItem(response.data);
          addNotification({
            type: 'success',
            message: 'Item equipped successfully!'
          });
          return response.data;
        } else {
          throw new Error(response.error || 'Failed to equip item');
        }
      },
      // Revert function
      () => {
        revertOptimisticUpdate();
      }
    );
  }, [updateInventoryItem, revertOptimisticUpdate, addNotification]);

  return {
    loadCharacters,
    createCharacter,
    selectCharacter,
    useItem,
    equipItem
  };
}

export function useGameSessionActions() {
  const { 
    setCurrentSession, 
    setCurrentDungeon, 
    setCurrentFloor,
    setGameState,
    addNotification 
  } = useGameStore();

  const startGameSession = useCallback(async (characterId: string, dungeonId: string) => {
    try {
      const response = await apiClient.startGameSession(characterId, dungeonId);
      
      if (response.success && response.data) {
        const session = response.data;
        setCurrentSession(session);
        addNotification({
          type: 'success',
          message: 'Game session started!'
        });
        return { success: true, session };
      } else {
        throw new Error(response.error || 'Failed to start game session');
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to start game session';
      addNotification({
        type: 'error',
        message
      });
      return { success: false, error: message };
    }
  }, [setCurrentSession, addNotification]);

  const endGameSession = useCallback(async (sessionId: string) => {
    try {
      const response = await apiClient.endGameSession(sessionId);
      
      if (response.success) {
        setCurrentSession(null);
        setCurrentDungeon(null);
        setCurrentFloor(null);
        setGameState(null);
        addNotification({
          type: 'info',
          message: 'Game session ended'
        });
        return { success: true };
      } else {
        throw new Error(response.error || 'Failed to end game session');
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to end game session';
      addNotification({
        type: 'error',
        message
      });
      return { success: false, error: message };
    }
  }, [setCurrentSession, setCurrentDungeon, setCurrentFloor, setGameState, addNotification]);

  return {
    startGameSession,
    endGameSession
  };
}

export function useCombatActions() {
  const { 
    setCurrentCombat, 
    setCombatLoading, 
    setLastAction,
    updateCharacter,
    addNotification 
  } = useGameStore();

  const startCombat = useCallback(async (sessionId: string, enemyId: string) => {
    setCombatLoading(true);
    try {
      const response = await apiClient.startCombat(sessionId, enemyId);
      
      if (response.success && response.data) {
        const combat = response.data;
        setCurrentCombat(combat);
        addNotification({
          type: 'info',
          message: 'Combat started!'
        });
        return { success: true, combat };
      } else {
        throw new Error(response.error || 'Failed to start combat');
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to start combat';
      addNotification({
        type: 'error',
        message
      });
      return { success: false, error: message };
    } finally {
      setCombatLoading(false);
    }
  }, [setCurrentCombat, setCombatLoading, addNotification]);

  const performAction = useCallback(async (combatId: string, action: any) => {
    setCombatLoading(true);
    try {
      const response = await apiClient.performCombatAction(combatId, action);
      
      if (response.success && response.data) {
        const { combat, character } = response.data;
        setCurrentCombat(combat);
        setLastAction(action);
        
        if (character) {
          updateCharacter(character);
        }

        // Check if combat ended
        if (!combat.is_active) {
          const winner = combat.winner;
          addNotification({
            type: winner === 'character' ? 'success' : 'error',
            message: winner === 'character' ? 'Victory!' : 'Defeat!'
          });
        }

        return { success: true, combat };
      } else {
        throw new Error(response.error || 'Failed to perform action');
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to perform action';
      addNotification({
        type: 'error',
        message
      });
      return { success: false, error: message };
    } finally {
      setCombatLoading(false);
    }
  }, [setCurrentCombat, setLastAction, updateCharacter, setCombatLoading, addNotification]);

  return {
    startCombat,
    performAction
  };
}

export function useNotifications() {
  const { notifications, removeNotification, clearNotifications } = useUI();

  const dismissNotification = useCallback((id: string) => {
    removeNotification(id);
  }, [removeNotification]);

  const clearAll = useCallback(() => {
    clearNotifications();
  }, [clearNotifications]);

  return {
    notifications,
    dismissNotification,
    clearAll
  };
}
