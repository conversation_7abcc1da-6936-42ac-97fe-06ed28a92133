import { neon } from '@netlify/neon';

const sql = neon();

// Database initialization and schema creation
export async function initializeDatabase() {
  try {
    // Users table - for authentication and basic user info
    await sql`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        username VARCHAR(50) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_login TIMESTAMP,
        is_active BOOLEAN DEFAULT true
      )
    `;

    // Characters table - player characters with stats
    await sql`
      CREATE TABLE IF NOT EXISTS characters (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
        level INTEGER DEFAULT 1,
        experience INTEGER DEFAULT 0,
        health INTEGER DEFAULT 100,
        max_health INTEGER DEFAULT 100,
        attack INTEGER DEFAULT 10,
        defense INTEGER DEFAULT 5,
        speed INTEGER DEFAULT 10,
        gold INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Items table - all game items (weapons, armor, consumables)
    await sql`
      CREATE TABLE IF NOT EXISTS items (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(100) NOT NULL,
        type VARCHAR(20) NOT NULL, -- 'weapon', 'armor', 'consumable'
        rarity VARCHAR(20) DEFAULT 'common', -- 'common', 'rare', 'epic', 'legendary'
        attack_bonus INTEGER DEFAULT 0,
        defense_bonus INTEGER DEFAULT 0,
        health_bonus INTEGER DEFAULT 0,
        speed_bonus INTEGER DEFAULT 0,
        heal_amount INTEGER DEFAULT 0,
        description TEXT,
        price INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Character inventory - many-to-many relationship
    await sql`
      CREATE TABLE IF NOT EXISTS character_inventory (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
        item_id UUID NOT NULL REFERENCES items(id) ON DELETE CASCADE,
        quantity INTEGER DEFAULT 1,
        is_equipped BOOLEAN DEFAULT false,
        acquired_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(character_id, item_id)
      )
    `;

    // Dungeons table - procedurally generated dungeons
    await sql`
      CREATE TABLE IF NOT EXISTS dungeons (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(100) NOT NULL,
        difficulty INTEGER DEFAULT 1,
        max_floors INTEGER DEFAULT 10,
        seed VARCHAR(100), -- for procedural generation
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Game sessions - active dungeon runs
    await sql`
      CREATE TABLE IF NOT EXISTS game_sessions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
        dungeon_id UUID NOT NULL REFERENCES dungeons(id) ON DELETE CASCADE,
        current_floor INTEGER DEFAULT 1,
        current_room INTEGER DEFAULT 1,
        session_state JSONB DEFAULT '{}', -- flexible state storage
        is_active BOOLEAN DEFAULT true,
        started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP,
        score INTEGER DEFAULT 0
      )
    `;

    // Combat sessions - turn-based combat instances
    await sql`
      CREATE TABLE IF NOT EXISTS combat_sessions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        game_session_id UUID NOT NULL REFERENCES game_sessions(id) ON DELETE CASCADE,
        character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
        enemy_data JSONB NOT NULL, -- enemy stats and info
        combat_state JSONB DEFAULT '{}', -- turn order, status effects, etc.
        current_turn INTEGER DEFAULT 1,
        character_turn BOOLEAN DEFAULT true,
        is_active BOOLEAN DEFAULT true,
        started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP,
        winner VARCHAR(20) -- 'character', 'enemy', 'draw'
      )
    `;

    // Combat actions - log of all combat actions for analytics
    await sql`
      CREATE TABLE IF NOT EXISTS combat_actions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        combat_session_id UUID NOT NULL REFERENCES combat_sessions(id) ON DELETE CASCADE,
        actor VARCHAR(20) NOT NULL, -- 'character' or 'enemy'
        action_type VARCHAR(50) NOT NULL, -- 'attack', 'defend', 'use_item', 'special'
        action_data JSONB DEFAULT '{}',
        damage_dealt INTEGER DEFAULT 0,
        turn_number INTEGER NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    console.log('Database schema initialized successfully');
  } catch (error) {
    console.error('Error initializing database:', error);
    throw error;
  }
}

// Create indexes for performance optimization
export async function createIndexes() {
  try {
    // User-related indexes
    await sql`CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active)`;

    // Character-related indexes
    await sql`CREATE INDEX IF NOT EXISTS idx_characters_user_id ON characters(user_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_characters_level ON characters(level)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_characters_updated_at ON characters(updated_at)`;

    // Inventory indexes
    await sql`CREATE INDEX IF NOT EXISTS idx_inventory_character_id ON character_inventory(character_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_inventory_item_id ON character_inventory(item_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_inventory_equipped ON character_inventory(character_id, is_equipped)`;

    // Game session indexes
    await sql`CREATE INDEX IF NOT EXISTS idx_sessions_character_id ON game_sessions(character_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_sessions_active ON game_sessions(is_active)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_sessions_started_at ON game_sessions(started_at)`;

    // Combat session indexes
    await sql`CREATE INDEX IF NOT EXISTS idx_combat_game_session ON combat_sessions(game_session_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_combat_character ON combat_sessions(character_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_combat_active ON combat_sessions(is_active)`;

    // Combat actions indexes
    await sql`CREATE INDEX IF NOT EXISTS idx_actions_combat_session ON combat_actions(combat_session_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_actions_turn ON combat_actions(combat_session_id, turn_number)`;

    console.log('Database indexes created successfully');
  } catch (error) {
    console.error('Error creating indexes:', error);
    throw error;
  }
}

export { sql };
