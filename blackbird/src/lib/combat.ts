import { sql } from './database';
import { getCharacterEffectiveStats } from './characters';
import { 
  CombatSession, 
  CombatState, 
  CombatAction, 
  CombatActionRequest,
  Enemy, 
  Character,
  StatusEffect,
  CombatActor,
  CombatWinner
} from '../types/game';
import { enemyTemplates } from './seed-data';
import { v4 as uuidv4 } from 'uuid';

// Create a new combat session
export async function createCombatSession(
  gameSessionId: string, 
  characterId: string, 
  enemyType: string
): Promise<CombatSession> {
  // Get character with effective stats
  const characterWithStats = await getCharacterEffectiveStats(characterId);
  if (!characterWithStats) {
    throw new Error('Character not found');
  }

  // Generate enemy from template
  const enemyTemplate = enemyTemplates[enemyType];
  if (!enemyTemplate) {
    throw new Error('Enemy type not found');
  }

  const enemy: Enemy = {
    id: uuidv4(),
    ...enemyTemplate
  };

  // Determine turn order based on speed
  const characterSpeed = characterWithStats.effective_stats.speed;
  const enemySpeed = enemy.speed;
  const characterGoesFirst = characterSpeed >= enemySpeed;

  // Initialize combat state
  const combatState: CombatState = {
    character_health: characterWithStats.health,
    enemy_health: enemy.health,
    character_effects: [],
    enemy_effects: [],
    turn_order: characterGoesFirst ? ['character', 'enemy'] : ['enemy', 'character'],
    action_history: []
  };

  // Create combat session in database
  const [combatSession] = await sql`
    INSERT INTO combat_sessions (
      game_session_id, 
      character_id, 
      enemy_data, 
      combat_state, 
      current_turn, 
      character_turn
    )
    VALUES (
      ${gameSessionId}, 
      ${characterId}, 
      ${JSON.stringify(enemy)}, 
      ${JSON.stringify(combatState)}, 
      1, 
      ${characterGoesFirst}
    )
    RETURNING *
  `;

  return {
    ...combatSession,
    enemy_data: enemy,
    combat_state: combatState
  } as CombatSession;
}

// Get combat session by ID
export async function getCombatSession(combatId: string): Promise<CombatSession | null> {
  const [combat] = await sql`
    SELECT * FROM combat_sessions 
    WHERE id = ${combatId} AND is_active = true
  `;

  if (!combat) return null;

  return {
    ...combat,
    enemy_data: typeof combat.enemy_data === 'string' ? JSON.parse(combat.enemy_data) : combat.enemy_data,
    combat_state: typeof combat.combat_state === 'string' ? JSON.parse(combat.combat_state) : combat.combat_state
  } as CombatSession;
}

// Process combat action
export async function processCombatAction(
  combatId: string, 
  action: CombatActionRequest
): Promise<{ combat: CombatSession; character?: Character }> {
  const combat = await getCombatSession(combatId);
  if (!combat) {
    throw new Error('Combat session not found');
  }

  if (!combat.is_active) {
    throw new Error('Combat session is not active');
  }

  // Get character data
  const character = await getCharacterEffectiveStats(combat.character_id);
  if (!character) {
    throw new Error('Character not found');
  }

  let updatedCombatState = { ...combat.combat_state };
  let damageDealt = 0;
  let updatedCharacter = character;

  // Process character action
  if (combat.character_turn) {
    const result = await processCharacterAction(combat, character, action);
    updatedCombatState = result.combatState;
    damageDealt = result.damageDealt;
    updatedCharacter = result.character || character;
  } else {
    throw new Error('Not character\'s turn');
  }

  // Log the action
  await sql`
    INSERT INTO combat_actions (
      combat_session_id, 
      actor, 
      action_type, 
      action_data, 
      damage_dealt, 
      turn_number
    )
    VALUES (
      ${combatId}, 
      'character', 
      ${action.action_type}, 
      ${JSON.stringify(action.action_data || {})}, 
      ${damageDealt}, 
      ${combat.current_turn}
    )
  `;

  // Check if enemy is defeated
  let winner: CombatWinner | undefined;
  let isActive = true;

  if (updatedCombatState.enemy_health <= 0) {
    winner = 'character';
    isActive = false;
    
    // Award experience and gold
    const enemy = combat.enemy_data;
    const newExperience = updatedCharacter.experience + enemy.experience_reward;
    const newGold = updatedCharacter.gold + enemy.gold_reward;
    
    await sql`
      UPDATE characters 
      SET experience = ${newExperience}, gold = ${newGold}
      WHERE id = ${combat.character_id}
    `;

    updatedCharacter = { ...updatedCharacter, experience: newExperience, gold: newGold };
  } else {
    // Process enemy turn
    const enemyResult = await processEnemyAction(combat, updatedCombatState);
    updatedCombatState = enemyResult.combatState;

    // Log enemy action
    await sql`
      INSERT INTO combat_actions (
        combat_session_id, 
        actor, 
        action_type, 
        action_data, 
        damage_dealt, 
        turn_number
      )
      VALUES (
        ${combatId}, 
        'enemy', 
        ${enemyResult.actionType}, 
        ${JSON.stringify(enemyResult.actionData)}, 
        ${enemyResult.damageDealt}, 
        ${combat.current_turn}
      )
    `;

    // Check if character is defeated
    if (updatedCombatState.character_health <= 0) {
      winner = 'enemy';
      isActive = false;
    }
  }

  // Update combat session
  const [updatedCombat] = await sql`
    UPDATE combat_sessions 
    SET 
      combat_state = ${JSON.stringify(updatedCombatState)},
      current_turn = ${combat.current_turn + 1},
      character_turn = ${!combat.character_turn},
      is_active = ${isActive},
      winner = ${winner || null},
      completed_at = ${!isActive ? new Date().toISOString() : null}
    WHERE id = ${combatId}
    RETURNING *
  `;

  // Update character health in database
  await sql`
    UPDATE characters 
    SET health = ${updatedCombatState.character_health}
    WHERE id = ${combat.character_id}
  `;

  return {
    combat: {
      ...updatedCombat,
      enemy_data: combat.enemy_data,
      combat_state: updatedCombatState
    } as CombatSession,
    character: { ...updatedCharacter, health: updatedCombatState.character_health }
  };
}

// Process character action
async function processCharacterAction(
  combat: CombatSession, 
  character: Character & { effective_stats: any }, 
  action: CombatActionRequest
): Promise<{ combatState: CombatState; damageDealt: number; character?: Character }> {
  let combatState = { ...combat.combat_state };
  let damageDealt = 0;
  let updatedCharacter = character;

  switch (action.action_type) {
    case 'attack':
      // Calculate damage
      const attackPower = character.effective_stats.attack;
      const enemyDefense = combat.enemy_data.defense;
      damageDealt = Math.max(1, attackPower - enemyDefense + Math.floor(Math.random() * 5) - 2);
      
      combatState.enemy_health = Math.max(0, combatState.enemy_health - damageDealt);
      break;

    case 'defend':
      // Defending reduces incoming damage next turn
      combatState.character_effects.push({
        id: uuidv4(),
        name: 'Defending',
        type: 'buff',
        duration: 1,
        effect: { defense_modifier: 5 }
      });
      break;

    case 'use_item':
      if (!action.item_id) {
        throw new Error('Item ID required for use_item action');
      }
      
      // This would integrate with the inventory system
      // For now, assume it's a health potion
      const healAmount = 30;
      const newHealth = Math.min(character.health + healAmount, character.max_health);
      combatState.character_health = newHealth;
      
      // Update character in database
      await sql`
        UPDATE characters 
        SET health = ${newHealth}
        WHERE id = ${character.id}
      `;
      
      updatedCharacter = { ...character, health: newHealth };
      break;

    default:
      throw new Error(`Unknown action type: ${action.action_type}`);
  }

  return { combatState, damageDealt, character: updatedCharacter };
}

// Process enemy action (AI)
async function processEnemyAction(
  combat: CombatSession, 
  combatState: CombatState
): Promise<{ combatState: CombatState; actionType: string; actionData: any; damageDealt: number }> {
  const enemy = combat.enemy_data;
  let updatedCombatState = { ...combatState };
  let damageDealt = 0;

  // Simple AI: mostly attack, sometimes defend if health is low
  const shouldDefend = enemy.health < enemy.max_health * 0.3 && Math.random() < 0.3;
  
  if (shouldDefend) {
    // Enemy defends
    updatedCombatState.enemy_effects.push({
      id: uuidv4(),
      name: 'Defending',
      type: 'buff',
      duration: 1,
      effect: { defense_modifier: 3 }
    });

    return {
      combatState: updatedCombatState,
      actionType: 'defend',
      actionData: {},
      damageDealt: 0
    };
  } else {
    // Enemy attacks
    const enemyAttack = enemy.attack;
    
    // Check if character is defending
    const defendingEffect = updatedCombatState.character_effects.find(e => e.name === 'Defending');
    const characterDefense = defendingEffect ? 
      (await getCharacterEffectiveStats(combat.character_id))?.effective_stats.defense + defendingEffect.effect.defense_modifier! :
      (await getCharacterEffectiveStats(combat.character_id))?.effective_stats.defense || 0;

    damageDealt = Math.max(1, enemyAttack - characterDefense + Math.floor(Math.random() * 3) - 1);
    updatedCombatState.character_health = Math.max(0, updatedCombatState.character_health - damageDealt);

    return {
      combatState: updatedCombatState,
      actionType: 'attack',
      actionData: { target: 'character' },
      damageDealt
    };
  }
}

// Apply status effects at turn start
function applyStatusEffects(combatState: CombatState, actor: CombatActor): CombatState {
  const effects = actor === 'character' ? combatState.character_effects : combatState.enemy_effects;
  const updatedState = { ...combatState };

  effects.forEach(effect => {
    // Apply damage/heal over time
    if (effect.effect.damage_per_turn) {
      if (actor === 'character') {
        updatedState.character_health = Math.max(0, updatedState.character_health - effect.effect.damage_per_turn);
      } else {
        updatedState.enemy_health = Math.max(0, updatedState.enemy_health - effect.effect.damage_per_turn);
      }
    }

    if (effect.effect.heal_per_turn) {
      if (actor === 'character') {
        // Would need character max health here
        updatedState.character_health += effect.effect.heal_per_turn;
      } else {
        updatedState.enemy_health += effect.effect.heal_per_turn;
      }
    }

    // Reduce duration
    effect.duration--;
  });

  // Remove expired effects
  if (actor === 'character') {
    updatedState.character_effects = updatedState.character_effects.filter(e => e.duration > 0);
  } else {
    updatedState.enemy_effects = updatedState.enemy_effects.filter(e => e.duration > 0);
  }

  return updatedState;
}

// Get combat history for analytics
export async function getCombatHistory(characterId: string, limit: number = 10): Promise<CombatSession[]> {
  const combats = await sql`
    SELECT * FROM combat_sessions 
    WHERE character_id = ${characterId} AND is_active = false
    ORDER BY completed_at DESC
    LIMIT ${limit}
  `;

  return combats.map(combat => ({
    ...combat,
    enemy_data: typeof combat.enemy_data === 'string' ? JSON.parse(combat.enemy_data) : combat.enemy_data,
    combat_state: typeof combat.combat_state === 'string' ? JSON.parse(combat.combat_state) : combat.combat_state
  })) as CombatSession[];
}
