import { sql } from './database';
import { Character, CreateCharacterRequest, InventoryItem } from '../types/game';

// Create a new character
export async function create<PERSON><PERSON>cter(userId: string, characterData: CreateCharacterRequest): Promise<Character> {
  const { name } = characterData;

  // Check if character name is already taken by this user
  const existingCharacter = await sql`
    SELECT id FROM characters 
    WHERE user_id = ${userId} AND name = ${name}
  `;

  if (existingCharacter.length > 0) {
    throw new Error('Character name already exists');
  }

  // Create character with default stats
  const [newCharacter] = await sql`
    INSERT INTO characters (user_id, name, level, experience, health, max_health, attack, defense, speed, gold)
    VALUES (${userId}, ${name}, 1, 0, 100, 100, 10, 5, 10, 50)
    RETURNING *
  `;

  // Give starting equipment
  await giveStartingEquipment(newCharacter.id);

  return newC<PERSON><PERSON> as Character;
}

// Give starting equipment to new character
async function giveStartingEquipment(characterId: string): Promise<void> {
  // Get basic starting items
  const startingItems = await sql`
    SELECT id FROM items 
    WHERE name IN ('Rusty Sword', 'Leather Vest', 'Health Potion')
  `;

  // Add items to character inventory
  for (const item of startingItems) {
    const quantity = item.name === 'Health Potion' ? 3 : 1;
    const isEquipped = item.name !== 'Health Potion';

    await sql`
      INSERT INTO character_inventory (character_id, item_id, quantity, is_equipped)
      VALUES (${characterId}, ${item.id}, ${quantity}, ${isEquipped})
    `;
  }
}

// Get character by ID
export async function getCharacterById(characterId: string, userId?: string): Promise<Character | null> {
  let query;
  
  if (userId) {
    query = sql`
      SELECT * FROM characters 
      WHERE id = ${characterId} AND user_id = ${userId}
    `;
  } else {
    query = sql`
      SELECT * FROM characters 
      WHERE id = ${characterId}
    `;
  }

  const [character] = await query;
  return character as Character || null;
}

// Get all characters for a user
export async function getUserCharacters(userId: string): Promise<Character[]> {
  const characters = await sql`
    SELECT * FROM characters 
    WHERE user_id = ${userId}
    ORDER BY created_at DESC
  `;

  return characters as Character[];
}

// Update character stats
export async function updateCharacterStats(characterId: string, updates: Partial<Character>): Promise<Character> {
  const allowedFields = ['health', 'max_health', 'attack', 'defense', 'speed', 'gold', 'experience', 'level'];
  const updateFields = Object.keys(updates).filter(key => allowedFields.includes(key));
  
  if (updateFields.length === 0) {
    throw new Error('No valid fields to update');
  }

  // Build dynamic update query
  const setClause = updateFields.map(field => `${field} = $${field}`).join(', ');
  const values = updateFields.reduce((acc, field) => {
    acc[field] = updates[field as keyof Character];
    return acc;
  }, {} as Record<string, any>);

  const [updatedCharacter] = await sql`
    UPDATE characters 
    SET ${sql.unsafe(setClause)}, updated_at = CURRENT_TIMESTAMP
    WHERE id = ${characterId}
    RETURNING *
  `.values(values);

  return updatedCharacter as Character;
}

// Level up character
export async function levelUpCharacter(characterId: string): Promise<Character> {
  const character = await getCharacterById(characterId);
  if (!character) {
    throw new Error('Character not found');
  }

  // Calculate experience needed for next level
  const experienceNeeded = character.level * 100;
  
  if (character.experience < experienceNeeded) {
    throw new Error('Not enough experience to level up');
  }

  // Level up stats
  const newLevel = character.level + 1;
  const healthIncrease = 20;
  const attackIncrease = 3;
  const defenseIncrease = 2;
  const speedIncrease = 1;

  const [updatedCharacter] = await sql`
    UPDATE characters 
    SET 
      level = ${newLevel},
      experience = ${character.experience - experienceNeeded},
      max_health = ${character.max_health + healthIncrease},
      health = ${character.max_health + healthIncrease}, -- Full heal on level up
      attack = ${character.attack + attackIncrease},
      defense = ${character.defense + defenseIncrease},
      speed = ${character.speed + speedIncrease},
      updated_at = CURRENT_TIMESTAMP
    WHERE id = ${characterId}
    RETURNING *
  `;

  return updatedCharacter as Character;
}

// Get character inventory
export async function getCharacterInventory(characterId: string): Promise<InventoryItem[]> {
  const inventory = await sql`
    SELECT 
      ci.*,
      i.name, i.type, i.rarity, i.attack_bonus, i.defense_bonus, 
      i.health_bonus, i.speed_bonus, i.heal_amount, i.description, i.price
    FROM character_inventory ci
    JOIN items i ON ci.item_id = i.id
    WHERE ci.character_id = ${characterId}
    ORDER BY ci.is_equipped DESC, i.type, i.name
  `;

  return inventory.map(item => ({
    id: item.id,
    character_id: item.character_id,
    item_id: item.item_id,
    quantity: item.quantity,
    is_equipped: item.is_equipped,
    acquired_at: item.acquired_at,
    item: {
      id: item.item_id,
      name: item.name,
      type: item.type,
      rarity: item.rarity,
      attack_bonus: item.attack_bonus,
      defense_bonus: item.defense_bonus,
      health_bonus: item.health_bonus,
      speed_bonus: item.speed_bonus,
      heal_amount: item.heal_amount,
      description: item.description,
      price: item.price,
      created_at: item.created_at
    }
  })) as InventoryItem[];
}

// Equip/unequip item
export async function toggleItemEquipped(characterId: string, inventoryItemId: string): Promise<InventoryItem> {
  const [inventoryItem] = await sql`
    SELECT ci.*, i.type 
    FROM character_inventory ci
    JOIN items i ON ci.item_id = i.id
    WHERE ci.id = ${inventoryItemId} AND ci.character_id = ${characterId}
  `;

  if (!inventoryItem) {
    throw new Error('Inventory item not found');
  }

  // If equipping, unequip other items of the same type first
  if (!inventoryItem.is_equipped && inventoryItem.type !== 'consumable') {
    await sql`
      UPDATE character_inventory 
      SET is_equipped = false
      WHERE character_id = ${characterId} 
        AND item_id IN (
          SELECT id FROM items WHERE type = ${inventoryItem.type}
        )
    `;
  }

  // Toggle equipped status
  const [updatedItem] = await sql`
    UPDATE character_inventory 
    SET is_equipped = ${!inventoryItem.is_equipped}
    WHERE id = ${inventoryItemId}
    RETURNING *
  `;

  return updatedItem as InventoryItem;
}

// Use consumable item
export async function useConsumableItem(characterId: string, inventoryItemId: string): Promise<{ character: Character; itemUsed: boolean }> {
  const character = await getCharacterById(characterId);
  if (!character) {
    throw new Error('Character not found');
  }

  const [inventoryItem] = await sql`
    SELECT ci.*, i.heal_amount, i.type
    FROM character_inventory ci
    JOIN items i ON ci.item_id = i.id
    WHERE ci.id = ${inventoryItemId} AND ci.character_id = ${characterId}
  `;

  if (!inventoryItem) {
    throw new Error('Inventory item not found');
  }

  if (inventoryItem.type !== 'consumable') {
    throw new Error('Item is not consumable');
  }

  if (inventoryItem.quantity <= 0) {
    throw new Error('No items left to use');
  }

  // Apply item effect (healing)
  const newHealth = Math.min(character.health + inventoryItem.heal_amount, character.max_health);
  
  // Update character health
  const updatedCharacter = await updateCharacterStats(characterId, { health: newHealth });

  // Decrease item quantity
  if (inventoryItem.quantity === 1) {
    // Remove item if quantity becomes 0
    await sql`
      DELETE FROM character_inventory 
      WHERE id = ${inventoryItemId}
    `;
  } else {
    await sql`
      UPDATE character_inventory 
      SET quantity = quantity - 1
      WHERE id = ${inventoryItemId}
    `;
  }

  return { character: updatedCharacter, itemUsed: true };
}

// Calculate character's effective stats (including equipment bonuses)
export async function getCharacterEffectiveStats(characterId: string): Promise<Character & { effective_stats: any }> {
  const character = await getCharacterById(characterId);
  if (!character) {
    throw new Error('Character not found');
  }

  // Get equipped items
  const equippedItems = await sql`
    SELECT i.attack_bonus, i.defense_bonus, i.health_bonus, i.speed_bonus
    FROM character_inventory ci
    JOIN items i ON ci.item_id = i.id
    WHERE ci.character_id = ${characterId} AND ci.is_equipped = true
  `;

  // Calculate bonuses
  const bonuses = equippedItems.reduce((acc, item) => ({
    attack: acc.attack + item.attack_bonus,
    defense: acc.defense + item.defense_bonus,
    health: acc.health + item.health_bonus,
    speed: acc.speed + item.speed_bonus
  }), { attack: 0, defense: 0, health: 0, speed: 0 });

  return {
    ...character,
    effective_stats: {
      attack: character.attack + bonuses.attack,
      defense: character.defense + bonuses.defense,
      max_health: character.max_health + bonuses.health,
      speed: character.speed + bonuses.speed
    }
  };
}
