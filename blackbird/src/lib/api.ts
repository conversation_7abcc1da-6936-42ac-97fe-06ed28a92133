import { ApiResponse } from '../types/game';

class ApiClient {
  private baseUrl: string;
  private token: string | null = null;

  constructor(baseUrl: string = '/api') {
    this.baseUrl = baseUrl;
  }

  setToken(token: string | null) {
    this.token = token;
  }

  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    return headers;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const config: RequestInit = {
      ...options,
      headers: {
        ...this.getHeaders(),
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // Auth endpoints
  async register(email: string, username: string, password: string) {
    return this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify({ email, username, password }),
    });
  }

  async login(email: string, password: string) {
    return this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  // Character endpoints
  async getCharacters() {
    return this.request('/characters');
  }

  async getCharacter(characterId: string) {
    return this.request(`/characters/${characterId}`);
  }

  async createCharacter(name: string) {
    return this.request('/characters', {
      method: 'POST',
      body: JSON.stringify({ name }),
    });
  }

  async getCharacterInventory(characterId: string) {
    return this.request(`/characters/${characterId}/inventory`);
  }

  async getCharacterStats(characterId: string) {
    return this.request(`/characters/${characterId}/stats`);
  }

  async equipItem(characterId: string, inventoryItemId: string) {
    return this.request(`/characters/${characterId}/equip`, {
      method: 'POST',
      body: JSON.stringify({ inventory_item_id: inventoryItemId }),
    });
  }

  async useItem(characterId: string, inventoryItemId: string) {
    return this.request(`/characters/${characterId}/use-item`, {
      method: 'POST',
      body: JSON.stringify({ inventory_item_id: inventoryItemId }),
    });
  }

  // Game session endpoints
  async startGameSession(characterId: string, dungeonId: string) {
    return this.request('/game/start', {
      method: 'POST',
      body: JSON.stringify({ character_id: characterId, dungeon_id: dungeonId }),
    });
  }

  async getGameSession(sessionId: string) {
    return this.request(`/game/session/${sessionId}`);
  }

  async moveToRoom(sessionId: string, roomId: number) {
    return this.request(`/game/session/${sessionId}/move`, {
      method: 'POST',
      body: JSON.stringify({ room_id: roomId }),
    });
  }

  async endGameSession(sessionId: string) {
    return this.request(`/game/session/${sessionId}/end`, {
      method: 'POST',
    });
  }

  // Combat endpoints
  async startCombat(sessionId: string, enemyId: string) {
    return this.request('/combat/start', {
      method: 'POST',
      body: JSON.stringify({ session_id: sessionId, enemy_id: enemyId }),
    });
  }

  async getCombatSession(combatId: string) {
    return this.request(`/combat/${combatId}`);
  }

  async performCombatAction(combatId: string, action: any) {
    return this.request(`/combat/${combatId}/action`, {
      method: 'POST',
      body: JSON.stringify(action),
    });
  }

  // Dungeon endpoints
  async getDungeons() {
    return this.request('/dungeons');
  }

  async getDungeon(dungeonId: string) {
    return this.request(`/dungeons/${dungeonId}`);
  }

  // Items endpoints
  async getItems() {
    return this.request('/items');
  }

  async getItem(itemId: string) {
    return this.request(`/items/${itemId}`);
  }

  // Admin endpoints
  async initializeDatabase() {
    return this.request('/init-db', {
      method: 'POST',
    });
  }

  async seedDatabase() {
    return this.request('/seed-data', {
      method: 'POST',
    });
  }
}

// Create singleton instance
export const apiClient = new ApiClient();

// Hook for React components
export function useApi() {
  return apiClient;
}

// Utility functions for common patterns
export async function withOptimisticUpdate<T>(
  optimisticUpdate: () => void,
  serverUpdate: () => Promise<T>,
  revertUpdate: () => void
): Promise<T> {
  // Apply optimistic update immediately
  optimisticUpdate();

  try {
    // Perform server update
    const result = await serverUpdate();
    return result;
  } catch (error) {
    // Revert optimistic update on failure
    revertUpdate();
    throw error;
  }
}

// Retry mechanism for failed requests
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxRetries) {
        throw lastError;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw lastError!;
}

// Debounce utility for frequent updates
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Cache for API responses
class ApiCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  set(key: string, data: any, ttl: number = 5 * 60 * 1000) { // 5 minutes default
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  get(key: string): any | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  clear() {
    this.cache.clear();
  }

  delete(key: string) {
    this.cache.delete(key);
  }
}

export const apiCache = new ApiCache();

// Cached API request wrapper
export async function cachedRequest<T>(
  key: string,
  requestFn: () => Promise<T>,
  ttl?: number
): Promise<T> {
  // Check cache first
  const cached = apiCache.get(key);
  if (cached) {
    return cached;
  }

  // Make request and cache result
  const result = await requestFn();
  apiCache.set(key, result, ttl);
  
  return result;
}
