import { sql } from './database';
import { Item, Enemy } from '../types/game';

// Seed items for the game
export async function seedItems() {
  const items = [
    // Weapons
    {
      name: 'Rusty Sword',
      type: 'weapon',
      rarity: 'common',
      attack_bonus: 5,
      defense_bonus: 0,
      health_bonus: 0,
      speed_bonus: 0,
      heal_amount: 0,
      description: 'A worn but functional sword.',
      price: 10
    },
    {
      name: 'Iron Blade',
      type: 'weapon',
      rarity: 'common',
      attack_bonus: 12,
      defense_bonus: 0,
      health_bonus: 0,
      speed_bonus: 0,
      heal_amount: 0,
      description: 'A sturdy iron sword.',
      price: 50
    },
    {
      name: 'Steel Sword',
      type: 'weapon',
      rarity: 'rare',
      attack_bonus: 20,
      defense_bonus: 2,
      health_bonus: 0,
      speed_bonus: 1,
      heal_amount: 0,
      description: 'A well-crafted steel blade.',
      price: 150
    },
    {
      name: 'Dragon Slayer',
      type: 'weapon',
      rarity: 'legendary',
      attack_bonus: 50,
      defense_bonus: 5,
      health_bonus: 10,
      speed_bonus: 3,
      heal_amount: 0,
      description: 'A legendary sword forged to slay dragons.',
      price: 1000
    },

    // Armor
    {
      name: 'Leather Vest',
      type: 'armor',
      rarity: 'common',
      attack_bonus: 0,
      defense_bonus: 3,
      health_bonus: 5,
      speed_bonus: 0,
      heal_amount: 0,
      description: 'Basic leather protection.',
      price: 15
    },
    {
      name: 'Chain Mail',
      type: 'armor',
      rarity: 'common',
      attack_bonus: 0,
      defense_bonus: 8,
      health_bonus: 10,
      speed_bonus: -1,
      heal_amount: 0,
      description: 'Interlocked metal rings provide good protection.',
      price: 75
    },
    {
      name: 'Plate Armor',
      type: 'armor',
      rarity: 'rare',
      attack_bonus: 0,
      defense_bonus: 15,
      health_bonus: 25,
      speed_bonus: -2,
      heal_amount: 0,
      description: 'Heavy metal plates offer excellent defense.',
      price: 200
    },
    {
      name: 'Dragon Scale Armor',
      type: 'armor',
      rarity: 'legendary',
      attack_bonus: 5,
      defense_bonus: 30,
      health_bonus: 50,
      speed_bonus: 0,
      heal_amount: 0,
      description: 'Armor crafted from dragon scales.',
      price: 800
    },

    // Consumables
    {
      name: 'Health Potion',
      type: 'consumable',
      rarity: 'common',
      attack_bonus: 0,
      defense_bonus: 0,
      health_bonus: 0,
      speed_bonus: 0,
      heal_amount: 30,
      description: 'Restores 30 health points.',
      price: 20
    },
    {
      name: 'Greater Health Potion',
      type: 'consumable',
      rarity: 'rare',
      attack_bonus: 0,
      defense_bonus: 0,
      health_bonus: 0,
      speed_bonus: 0,
      heal_amount: 75,
      description: 'Restores 75 health points.',
      price: 60
    },
    {
      name: 'Elixir of Life',
      type: 'consumable',
      rarity: 'legendary',
      attack_bonus: 0,
      defense_bonus: 0,
      health_bonus: 0,
      speed_bonus: 0,
      heal_amount: 150,
      description: 'Fully restores health and provides temporary boost.',
      price: 200
    }
  ];

  try {
    for (const item of items) {
      await sql`
        INSERT INTO items (name, type, rarity, attack_bonus, defense_bonus, health_bonus, speed_bonus, heal_amount, description, price)
        VALUES (${item.name}, ${item.type}, ${item.rarity}, ${item.attack_bonus}, ${item.defense_bonus}, ${item.health_bonus}, ${item.speed_bonus}, ${item.heal_amount}, ${item.description}, ${item.price})
        ON CONFLICT (name) DO NOTHING
      `;
    }
    console.log('Items seeded successfully');
  } catch (error) {
    console.error('Error seeding items:', error);
    throw error;
  }
}

// Seed dungeons
export async function seedDungeons() {
  const dungeons = [
    {
      name: 'Goblin Cave',
      difficulty: 1,
      max_floors: 5,
      seed: 'goblin_cave_001'
    },
    {
      name: 'Dark Forest',
      difficulty: 2,
      max_floors: 8,
      seed: 'dark_forest_001'
    },
    {
      name: 'Ancient Ruins',
      difficulty: 3,
      max_floors: 10,
      seed: 'ancient_ruins_001'
    },
    {
      name: 'Dragon\'s Lair',
      difficulty: 5,
      max_floors: 15,
      seed: 'dragon_lair_001'
    }
  ];

  try {
    for (const dungeon of dungeons) {
      await sql`
        INSERT INTO dungeons (name, difficulty, max_floors, seed)
        VALUES (${dungeon.name}, ${dungeon.difficulty}, ${dungeon.max_floors}, ${dungeon.seed})
        ON CONFLICT (name) DO NOTHING
      `;
    }
    console.log('Dungeons seeded successfully');
  } catch (error) {
    console.error('Error seeding dungeons:', error);
    throw error;
  }
}

// Enemy templates for procedural generation
export const enemyTemplates: Record<string, Omit<Enemy, 'id'>> = {
  goblin: {
    name: 'Goblin',
    level: 1,
    health: 25,
    max_health: 25,
    attack: 8,
    defense: 2,
    speed: 12,
    experience_reward: 15,
    gold_reward: 5
  },
  orc: {
    name: 'Orc',
    level: 2,
    health: 45,
    max_health: 45,
    attack: 15,
    defense: 5,
    speed: 8,
    experience_reward: 30,
    gold_reward: 12
  },
  skeleton: {
    name: 'Skeleton',
    level: 2,
    health: 35,
    max_health: 35,
    attack: 12,
    defense: 8,
    speed: 10,
    experience_reward: 25,
    gold_reward: 8
  },
  troll: {
    name: 'Troll',
    level: 4,
    health: 80,
    max_health: 80,
    attack: 25,
    defense: 12,
    speed: 6,
    experience_reward: 75,
    gold_reward: 30
  },
  dragon: {
    name: 'Dragon',
    level: 10,
    health: 200,
    max_health: 200,
    attack: 50,
    defense: 25,
    speed: 15,
    experience_reward: 500,
    gold_reward: 200
  }
};

export async function seedAll() {
  await seedItems();
  await seedDungeons();
  console.log('All seed data created successfully');
}
