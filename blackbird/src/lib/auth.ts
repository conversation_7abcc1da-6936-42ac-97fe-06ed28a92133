import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { sql } from './database';
import { User, CreateUserRequest, LoginRequest } from '../types/game';

// JWT secret - in production, this should be in environment variables
const JWT_SECRET = Netlify.env.get('JWT_SECRET') || 'your-super-secret-jwt-key-change-in-production';
const JWT_EXPIRES_IN = '7d';

// Hash password
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
}

// Verify password
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

// Generate JWT token
export function generateToken(userId: string, email: string): string {
  return jwt.sign(
    { userId, email },
    JWT_SECRET,
    { expiresIn: JWT_EXPIRES_IN }
  );
}

// Verify JWT token
export function verifyToken(token: string): { userId: string; email: string } | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string; email: string };
    return decoded;
  } catch (error) {
    return null;
  }
}

// Extract token from Authorization header
export function extractTokenFromHeader(authHeader: string | null): string | null {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Create new user
export async function createUser(userData: CreateUserRequest): Promise<User> {
  const { email, username, password } = userData;

  // Check if user already exists
  const existingUser = await sql`
    SELECT id FROM users 
    WHERE email = ${email} OR username = ${username}
  `;

  if (existingUser.length > 0) {
    throw new Error('User with this email or username already exists');
  }

  // Hash password
  const passwordHash = await hashPassword(password);

  // Create user
  const [newUser] = await sql`
    INSERT INTO users (email, username, password_hash)
    VALUES (${email}, ${username}, ${passwordHash})
    RETURNING id, email, username, created_at, updated_at, last_login, is_active
  `;

  return newUser as User;
}

// Authenticate user
export async function authenticateUser(loginData: LoginRequest): Promise<{ user: User; token: string }> {
  const { email, password } = loginData;

  // Find user by email
  const [user] = await sql`
    SELECT id, email, username, password_hash, created_at, updated_at, last_login, is_active
    FROM users 
    WHERE email = ${email} AND is_active = true
  `;

  if (!user) {
    throw new Error('Invalid email or password');
  }

  // Verify password
  const isValidPassword = await verifyPassword(password, user.password_hash);
  if (!isValidPassword) {
    throw new Error('Invalid email or password');
  }

  // Update last login
  await sql`
    UPDATE users 
    SET last_login = CURRENT_TIMESTAMP 
    WHERE id = ${user.id}
  `;

  // Generate token
  const token = generateToken(user.id, user.email);

  // Remove password hash from response
  const { password_hash, ...userWithoutPassword } = user;

  return {
    user: userWithoutPassword as User,
    token
  };
}

// Get user by ID
export async function getUserById(userId: string): Promise<User | null> {
  const [user] = await sql`
    SELECT id, email, username, created_at, updated_at, last_login, is_active
    FROM users 
    WHERE id = ${userId} AND is_active = true
  `;

  return user as User || null;
}

// Middleware to authenticate requests
export async function authenticateRequest(request: Request): Promise<User | null> {
  const authHeader = request.headers.get('Authorization');
  const token = extractTokenFromHeader(authHeader);

  if (!token) {
    return null;
  }

  const decoded = verifyToken(token);
  if (!decoded) {
    return null;
  }

  const user = await getUserById(decoded.userId);
  return user;
}

// Validate email format
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Validate username format
export function isValidUsername(username: string): boolean {
  // Username should be 3-50 characters, alphanumeric and underscores only
  const usernameRegex = /^[a-zA-Z0-9_]{3,50}$/;
  return usernameRegex.test(username);
}

// Validate password strength
export function isValidPassword(password: string): { valid: boolean; message?: string } {
  if (password.length < 8) {
    return { valid: false, message: 'Password must be at least 8 characters long' };
  }

  if (!/(?=.*[a-z])/.test(password)) {
    return { valid: false, message: 'Password must contain at least one lowercase letter' };
  }

  if (!/(?=.*[A-Z])/.test(password)) {
    return { valid: false, message: 'Password must contain at least one uppercase letter' };
  }

  if (!/(?=.*\d)/.test(password)) {
    return { valid: false, message: 'Password must contain at least one number' };
  }

  return { valid: true };
}

// Validate user registration data
export function validateRegistrationData(data: CreateUserRequest): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!isValidEmail(data.email)) {
    errors.push('Invalid email format');
  }

  if (!isValidUsername(data.username)) {
    errors.push('Username must be 3-50 characters and contain only letters, numbers, and underscores');
  }

  const passwordValidation = isValidPassword(data.password);
  if (!passwordValidation.valid) {
    errors.push(passwordValidation.message!);
  }

  return {
    valid: errors.length === 0,
    errors
  };
}
