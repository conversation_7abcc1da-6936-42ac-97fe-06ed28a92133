import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { 
  User, 
  Character, 
  GameSession, 
  CombatSession, 
  InventoryItem,
  GameState,
  Dungeon,
  DungeonFloor
} from '../types/game';

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

interface CharacterState {
  characters: Character[];
  currentCharacter: Character | null;
  characterInventory: InventoryItem[];
  isLoading: boolean;
}

interface GameSessionState {
  currentSession: GameSession | null;
  currentDungeon: Dungeon | null;
  currentFloor: DungeonFloor | null;
  gameState: GameState | null;
  isLoading: boolean;
}

interface CombatState {
  currentCombat: CombatSession | null;
  isInCombat: boolean;
  isLoading: boolean;
  lastAction: any;
}

interface UIState {
  currentView: 'menu' | 'character-select' | 'dungeon' | 'combat' | 'inventory';
  showInventory: boolean;
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'info' | 'warning';
    message: string;
    timestamp: number;
  }>;
}

interface GameStore extends AuthState, CharacterState, GameSessionState, CombatState, UIState {
  // Auth actions
  setAuth: (user: User, token: string) => void;
  logout: () => void;
  setAuthLoading: (loading: boolean) => void;

  // Character actions
  setCharacters: (characters: Character[]) => void;
  setCurrentCharacter: (character: Character | null) => void;
  updateCharacter: (character: Character) => void;
  setCharacterInventory: (inventory: InventoryItem[]) => void;
  updateInventoryItem: (item: InventoryItem) => void;
  removeInventoryItem: (itemId: string) => void;
  setCharacterLoading: (loading: boolean) => void;

  // Game session actions
  setCurrentSession: (session: GameSession | null) => void;
  setCurrentDungeon: (dungeon: Dungeon | null) => void;
  setCurrentFloor: (floor: DungeonFloor | null) => void;
  setGameState: (state: GameState | null) => void;
  setGameLoading: (loading: boolean) => void;

  // Combat actions
  setCurrentCombat: (combat: CombatSession | null) => void;
  setInCombat: (inCombat: boolean) => void;
  setCombatLoading: (loading: boolean) => void;
  setLastAction: (action: any) => void;

  // UI actions
  setCurrentView: (view: UIState['currentView']) => void;
  setShowInventory: (show: boolean) => void;
  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;

  // Optimistic updates
  optimisticCharacterUpdate: (updates: Partial<Character>) => void;
  revertOptimisticUpdate: () => void;

  // State synchronization
  syncWithServer: () => Promise<void>;
}

export const useGameStore = create<GameStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        
        characters: [],
        currentCharacter: null,
        characterInventory: [],
        
        currentSession: null,
        currentDungeon: null,
        currentFloor: null,
        gameState: null,
        
        currentCombat: null,
        isInCombat: false,
        lastAction: null,
        
        currentView: 'menu',
        showInventory: false,
        notifications: [],

        // Auth actions
        setAuth: (user, token) => set({
          user,
          token,
          isAuthenticated: true,
          isLoading: false
        }),

        logout: () => set({
          user: null,
          token: null,
          isAuthenticated: false,
          currentCharacter: null,
          characters: [],
          characterInventory: [],
          currentSession: null,
          currentDungeon: null,
          currentFloor: null,
          gameState: null,
          currentCombat: null,
          isInCombat: false,
          currentView: 'menu'
        }),

        setAuthLoading: (loading) => set({ isLoading: loading }),

        // Character actions
        setCharacters: (characters) => set({ characters }),
        
        setCurrentCharacter: (character) => set({ 
          currentCharacter: character,
          currentView: character ? 'dungeon' : 'character-select'
        }),

        updateCharacter: (character) => set((state) => ({
          currentCharacter: state.currentCharacter?.id === character.id ? character : state.currentCharacter,
          characters: state.characters.map(c => c.id === character.id ? character : c)
        })),

        setCharacterInventory: (inventory) => set({ characterInventory: inventory }),

        updateInventoryItem: (item) => set((state) => ({
          characterInventory: state.characterInventory.map(i => i.id === item.id ? item : i)
        })),

        removeInventoryItem: (itemId) => set((state) => ({
          characterInventory: state.characterInventory.filter(i => i.id !== itemId)
        })),

        setCharacterLoading: (loading) => set({ isLoading: loading }),

        // Game session actions
        setCurrentSession: (session) => set({ currentSession: session }),
        setCurrentDungeon: (dungeon) => set({ currentDungeon: dungeon }),
        setCurrentFloor: (floor) => set({ currentFloor: floor }),
        setGameState: (state) => set({ gameState: state }),
        setGameLoading: (loading) => set({ isLoading: loading }),

        // Combat actions
        setCurrentCombat: (combat) => set({ 
          currentCombat: combat,
          isInCombat: !!combat,
          currentView: combat ? 'combat' : 'dungeon'
        }),

        setInCombat: (inCombat) => set({ isInCombat: inCombat }),
        setCombatLoading: (loading) => set({ isLoading: loading }),
        setLastAction: (action) => set({ lastAction: action }),

        // UI actions
        setCurrentView: (view) => set({ currentView: view }),
        setShowInventory: (show) => set({ showInventory: show }),

        addNotification: (notification) => set((state) => ({
          notifications: [...state.notifications, {
            ...notification,
            id: Date.now().toString(),
            timestamp: Date.now()
          }]
        })),

        removeNotification: (id) => set((state) => ({
          notifications: state.notifications.filter(n => n.id !== id)
        })),

        clearNotifications: () => set({ notifications: [] }),

        // Optimistic updates
        optimisticCharacterUpdate: (updates) => set((state) => {
          if (!state.currentCharacter) return state;
          
          const updatedCharacter = { ...state.currentCharacter, ...updates };
          return {
            currentCharacter: updatedCharacter,
            characters: state.characters.map(c => 
              c.id === updatedCharacter.id ? updatedCharacter : c
            )
          };
        }),

        revertOptimisticUpdate: () => {
          // This would revert to the last known server state
          // Implementation depends on how you want to handle rollbacks
        },

        // State synchronization
        syncWithServer: async () => {
          const state = get();
          if (!state.isAuthenticated || !state.token) return;

          try {
            // Sync character data
            if (state.currentCharacter) {
              const response = await fetch(`/api/characters/${state.currentCharacter.id}`, {
                headers: {
                  'Authorization': `Bearer ${state.token}`,
                  'Content-Type': 'application/json'
                }
              });

              if (response.ok) {
                const { data: character } = await response.json();
                set({ currentCharacter: character });
              }
            }

            // Sync game session if active
            if (state.currentSession) {
              // Implement session sync logic
            }

            // Sync combat if active
            if (state.currentCombat) {
              // Implement combat sync logic
            }

          } catch (error) {
            console.error('Failed to sync with server:', error);
          }
        }
      }),
      {
        name: 'blackbird-game-store',
        partialize: (state) => ({
          user: state.user,
          token: state.token,
          isAuthenticated: state.isAuthenticated,
          currentCharacter: state.currentCharacter,
          currentView: state.currentView
        })
      }
    ),
    { name: 'GameStore' }
  )
);

// Selectors for common state combinations
export const useAuth = () => useGameStore((state) => ({
  user: state.user,
  token: state.token,
  isAuthenticated: state.isAuthenticated,
  isLoading: state.isLoading,
  setAuth: state.setAuth,
  logout: state.logout,
  setAuthLoading: state.setAuthLoading
}));

export const useCharacter = () => useGameStore((state) => ({
  characters: state.characters,
  currentCharacter: state.currentCharacter,
  characterInventory: state.characterInventory,
  isLoading: state.isLoading,
  setCharacters: state.setCharacters,
  setCurrentCharacter: state.setCurrentCharacter,
  updateCharacter: state.updateCharacter,
  setCharacterInventory: state.setCharacterInventory,
  updateInventoryItem: state.updateInventoryItem,
  removeInventoryItem: state.removeInventoryItem
}));

export const useCombat = () => useGameStore((state) => ({
  currentCombat: state.currentCombat,
  isInCombat: state.isInCombat,
  isLoading: state.isLoading,
  lastAction: state.lastAction,
  setCurrentCombat: state.setCurrentCombat,
  setInCombat: state.setInCombat,
  setCombatLoading: state.setCombatLoading,
  setLastAction: state.setLastAction
}));

export const useUI = () => useGameStore((state) => ({
  currentView: state.currentView,
  showInventory: state.showInventory,
  notifications: state.notifications,
  setCurrentView: state.setCurrentView,
  setShowInventory: state.setShowInventory,
  addNotification: state.addNotification,
  removeNotification: state.removeNotification,
  clearNotifications: state.clearNotifications
}));
