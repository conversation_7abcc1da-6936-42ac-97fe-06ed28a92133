'use client';

import { useEffect, useState } from 'react';
import { useCharacter } from '../store/gameStore';
import { useCharacterActions, useAuthActions } from '../hooks/useGameActions';

export default function CharacterSelect() {
  const { characters, isLoading } = useCharacter();
  const { loadCharacters, createCharacter, select<PERSON>haracter } = useCharacterActions();
  const { logout } = useAuthActions();
  
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newCharacterName, setNewCharacterName] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  useEffect(() => {
    loadCharacters();
  }, [loadCharacters]);

  const handleCreateCharacter = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newCharacterName.trim()) return;

    setIsCreating(true);
    try {
      const result = await create<PERSON>haracter({ name: newCharacterName.trim() });
      if (result.success) {
        setNewCharacterName('');
        setShowCreateForm(false);
      }
    } finally {
      setIsCreating(false);
    }
  };

  const handleSelectCharacter = (character: any) => {
    selectCharacter(character);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading characters...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-4xl font-bold text-white">Select Character</h1>
          <button
            onClick={logout}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
          >
            Logout
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {characters.map((character) => (
            <div
              key={character.id}
              className="bg-black/20 backdrop-blur-sm border border-white/10 rounded-lg p-6 hover:bg-black/30 transition-colors cursor-pointer"
              onClick={() => handleSelectCharacter(character)}
            >
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-2xl font-bold text-white">
                    {character.name.charAt(0).toUpperCase()}
                  </span>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">{character.name}</h3>
                <div className="text-gray-300 space-y-1">
                  <p>Level {character.level}</p>
                  <p>HP: {character.health}/{character.max_health}</p>
                  <p>Gold: {character.gold}</p>
                </div>
                <div className="mt-4 grid grid-cols-2 gap-2 text-sm text-gray-400">
                  <div>ATK: {character.attack}</div>
                  <div>DEF: {character.defense}</div>
                  <div>SPD: {character.speed}</div>
                  <div>EXP: {character.experience}</div>
                </div>
              </div>
            </div>
          ))}

          {/* Create New Character Card */}
          <div
            className="bg-black/10 backdrop-blur-sm border-2 border-dashed border-white/20 rounded-lg p-6 hover:bg-black/20 transition-colors cursor-pointer flex items-center justify-center"
            onClick={() => setShowCreateForm(true)}
          >
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-3xl text-white">+</span>
              </div>
              <p className="text-white font-medium">Create New Character</p>
            </div>
          </div>
        </div>

        {/* Create Character Modal */}
        {showCreateForm && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
            <div className="bg-gray-900 rounded-lg p-6 w-full max-w-md">
              <h2 className="text-2xl font-bold text-white mb-4">Create New Character</h2>
              
              <form onSubmit={handleCreateCharacter}>
                <div className="mb-4">
                  <label htmlFor="characterName" className="block text-sm font-medium text-gray-300 mb-2">
                    Character Name
                  </label>
                  <input
                    type="text"
                    id="characterName"
                    value={newCharacterName}
                    onChange={(e) => setNewCharacterName(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter character name"
                    maxLength={50}
                    required
                  />
                </div>

                <div className="flex gap-3">
                  <button
                    type="button"
                    onClick={() => {
                      setShowCreateForm(false);
                      setNewCharacterName('');
                    }}
                    className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isCreating || !newCharacterName.trim()}
                    className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                  >
                    {isCreating ? 'Creating...' : 'Create'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
