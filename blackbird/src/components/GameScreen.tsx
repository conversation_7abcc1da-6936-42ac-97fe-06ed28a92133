'use client';

import { useGameStore, useCharacter, useCombat, useUI } from '../store/gameStore';
import { useAuthActions } from '../hooks/useGameActions';

export default function GameScreen() {
  const { currentView, setCurrentView } = useGameStore();
  const { currentCharacter } = useCharacter();
  const { isInCombat } = useCombat();
  const { logout } = useAuthActions();

  if (!currentCharacter) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-white text-xl">No character selected</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Header */}
      <div className="bg-black/20 backdrop-blur-sm border-b border-white/10 p-4">
        <div className="max-w-6xl mx-auto flex justify-between items-center">
          <div className="flex items-center space-x-6">
            <h1 className="text-2xl font-bold text-white">{currentCharacter.name}</h1>
            <div className="flex items-center space-x-4 text-sm text-gray-300">
              <span>Level {currentCharacter.level}</span>
              <span>HP: {currentCharacter.health}/{currentCharacter.max_health}</span>
              <span>Gold: {currentCharacter.gold}</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setCurrentView('character-select')}
              className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
            >
              Change Character
            </button>
            <button
              onClick={logout}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
            >
              Logout
            </button>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="bg-black/10 backdrop-blur-sm border-b border-white/10 p-4">
        <div className="max-w-6xl mx-auto">
          <nav className="flex space-x-4">
            <button
              onClick={() => setCurrentView('dungeon')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                currentView === 'dungeon'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Dungeon
            </button>
            <button
              onClick={() => setCurrentView('inventory')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                currentView === 'inventory'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Inventory
            </button>
            {isInCombat && (
              <button
                onClick={() => setCurrentView('combat')}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  currentView === 'combat'
                    ? 'bg-red-600 text-white'
                    : 'bg-red-700 text-red-300 hover:bg-red-600'
                }`}
              >
                Combat!
              </button>
            )}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto p-4">
        {currentView === 'dungeon' && <DungeonView />}
        {currentView === 'inventory' && <InventoryView />}
        {currentView === 'combat' && <CombatView />}
      </div>
    </div>
  );
}

function DungeonView() {
  return (
    <div className="bg-black/20 backdrop-blur-sm border border-white/10 rounded-lg p-6">
      <h2 className="text-2xl font-bold text-white mb-4">Dungeon Explorer</h2>
      <div className="text-gray-300">
        <p className="mb-4">Welcome to the dungeon! This is where you'll explore procedurally generated dungeons, fight enemies, and find treasure.</p>
        <div className="bg-gray-800 rounded-lg p-4">
          <p className="text-yellow-400 font-medium">Coming Soon:</p>
          <ul className="mt-2 space-y-1 text-sm">
            <li>• Procedural dungeon generation</li>
            <li>• Room exploration</li>
            <li>• Enemy encounters</li>
            <li>• Treasure chests</li>
            <li>• Boss battles</li>
          </ul>
        </div>
      </div>
    </div>
  );
}

function InventoryView() {
  const { characterInventory } = useCharacter();

  return (
    <div className="bg-black/20 backdrop-blur-sm border border-white/10 rounded-lg p-6">
      <h2 className="text-2xl font-bold text-white mb-4">Inventory</h2>
      
      {characterInventory.length === 0 ? (
        <div className="text-gray-400 text-center py-8">
          <p>Your inventory is empty.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {characterInventory.map((inventoryItem) => (
            <div
              key={inventoryItem.id}
              className={`bg-gray-800 rounded-lg p-4 border-2 ${
                inventoryItem.is_equipped
                  ? 'border-green-500'
                  : 'border-gray-600'
              }`}
            >
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-bold text-white">{inventoryItem.item.name}</h3>
                {inventoryItem.is_equipped && (
                  <span className="text-xs bg-green-600 text-white px-2 py-1 rounded">
                    Equipped
                  </span>
                )}
              </div>
              
              <p className="text-sm text-gray-400 mb-2 capitalize">
                {inventoryItem.item.type} • {inventoryItem.item.rarity}
              </p>
              
              {inventoryItem.item.description && (
                <p className="text-sm text-gray-300 mb-3">
                  {inventoryItem.item.description}
                </p>
              )}
              
              <div className="text-xs text-gray-400 space-y-1">
                {inventoryItem.item.attack_bonus > 0 && (
                  <div>Attack: +{inventoryItem.item.attack_bonus}</div>
                )}
                {inventoryItem.item.defense_bonus > 0 && (
                  <div>Defense: +{inventoryItem.item.defense_bonus}</div>
                )}
                {inventoryItem.item.health_bonus > 0 && (
                  <div>Health: +{inventoryItem.item.health_bonus}</div>
                )}
                {inventoryItem.item.speed_bonus > 0 && (
                  <div>Speed: +{inventoryItem.item.speed_bonus}</div>
                )}
                {inventoryItem.item.heal_amount > 0 && (
                  <div>Heals: {inventoryItem.item.heal_amount} HP</div>
                )}
              </div>
              
              {inventoryItem.quantity > 1 && (
                <div className="mt-2 text-sm text-yellow-400">
                  Quantity: {inventoryItem.quantity}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

function CombatView() {
  const { currentCombat } = useCombat();

  if (!currentCombat) {
    return (
      <div className="bg-black/20 backdrop-blur-sm border border-white/10 rounded-lg p-6">
        <h2 className="text-2xl font-bold text-white mb-4">Combat</h2>
        <p className="text-gray-300">No active combat session.</p>
      </div>
    );
  }

  return (
    <div className="bg-black/20 backdrop-blur-sm border border-white/10 rounded-lg p-6">
      <h2 className="text-2xl font-bold text-white mb-4">Combat</h2>
      <div className="text-gray-300">
        <p className="mb-4">Combat system is ready! You're fighting: {currentCombat.enemy_data.name}</p>
        <div className="bg-gray-800 rounded-lg p-4">
          <p className="text-red-400 font-medium">Combat Features:</p>
          <ul className="mt-2 space-y-1 text-sm">
            <li>• Turn-based combat</li>
            <li>• Attack, defend, and use items</li>
            <li>• Status effects</li>
            <li>• Experience and gold rewards</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
