// User and Authentication Types
export interface User {
  id: string;
  email: string;
  username: string;
  created_at: string;
  updated_at: string;
  last_login?: string;
  is_active: boolean;
}

export interface CreateUserRequest {
  email: string;
  username: string;
  password: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

// Character Types
export interface Character {
  id: string;
  user_id: string;
  name: string;
  level: number;
  experience: number;
  health: number;
  max_health: number;
  attack: number;
  defense: number;
  speed: number;
  gold: number;
  created_at: string;
  updated_at: string;
}

export interface CreateCharacterRequest {
  name: string;
}

export interface CharacterStats {
  health: number;
  max_health: number;
  attack: number;
  defense: number;
  speed: number;
}

// Item Types
export type ItemType = 'weapon' | 'armor' | 'consumable';
export type ItemRarity = 'common' | 'rare' | 'epic' | 'legendary';

export interface Item {
  id: string;
  name: string;
  type: ItemType;
  rarity: ItemRarity;
  attack_bonus: number;
  defense_bonus: number;
  health_bonus: number;
  speed_bonus: number;
  heal_amount: number;
  description?: string;
  price: number;
  created_at: string;
}

export interface InventoryItem {
  id: string;
  character_id: string;
  item_id: string;
  quantity: number;
  is_equipped: boolean;
  acquired_at: string;
  item: Item; // populated via join
}

// Dungeon Types
export interface Dungeon {
  id: string;
  name: string;
  difficulty: number;
  max_floors: number;
  seed?: string;
  created_at: string;
}

export interface DungeonRoom {
  id: number;
  type: 'combat' | 'treasure' | 'shop' | 'boss';
  enemies?: Enemy[];
  treasure?: Item[];
  completed: boolean;
}

export interface DungeonFloor {
  floor_number: number;
  rooms: DungeonRoom[];
  current_room: number;
}

// Enemy Types
export interface Enemy {
  id: string;
  name: string;
  level: number;
  health: number;
  max_health: number;
  attack: number;
  defense: number;
  speed: number;
  experience_reward: number;
  gold_reward: number;
  loot_table?: Item[];
}

// Game Session Types
export interface GameSession {
  id: string;
  character_id: string;
  dungeon_id: string;
  current_floor: number;
  current_room: number;
  session_state: Record<string, any>;
  is_active: boolean;
  started_at: string;
  completed_at?: string;
  score: number;
}

export interface GameState {
  session: GameSession;
  character: Character;
  dungeon: Dungeon;
  current_floor: DungeonFloor;
  inventory: InventoryItem[];
}

// Combat Types
export type CombatActionType = 'attack' | 'defend' | 'use_item' | 'special';
export type CombatActor = 'character' | 'enemy';
export type CombatWinner = 'character' | 'enemy' | 'draw';

export interface CombatSession {
  id: string;
  game_session_id: string;
  character_id: string;
  enemy_data: Enemy;
  combat_state: CombatState;
  current_turn: number;
  character_turn: boolean;
  is_active: boolean;
  started_at: string;
  completed_at?: string;
  winner?: CombatWinner;
}

export interface CombatState {
  character_health: number;
  enemy_health: number;
  character_effects: StatusEffect[];
  enemy_effects: StatusEffect[];
  turn_order: CombatActor[];
  action_history: CombatAction[];
}

export interface StatusEffect {
  id: string;
  name: string;
  type: 'buff' | 'debuff';
  duration: number;
  effect: {
    attack_modifier?: number;
    defense_modifier?: number;
    speed_modifier?: number;
    damage_per_turn?: number;
    heal_per_turn?: number;
  };
}

export interface CombatAction {
  id: string;
  combat_session_id: string;
  actor: CombatActor;
  action_type: CombatActionType;
  action_data: Record<string, any>;
  damage_dealt: number;
  turn_number: number;
  created_at: string;
}

export interface CombatActionRequest {
  action_type: CombatActionType;
  target?: string;
  item_id?: string;
  action_data?: Record<string, any>;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  has_more: boolean;
}

// Game Events for Real-time Updates
export type GameEventType = 
  | 'combat_start'
  | 'combat_action'
  | 'combat_end'
  | 'level_up'
  | 'item_acquired'
  | 'room_entered'
  | 'floor_completed';

export interface GameEvent {
  type: GameEventType;
  session_id: string;
  character_id: string;
  data: Record<string, any>;
  timestamp: string;
}
