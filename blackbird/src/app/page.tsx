"use client";

import { useEffect } from "react";
import { useGameStore } from "../store/gameStore";
import AuthScreen from "../components/AuthScreen";
import CharacterSelect from "../components/CharacterSelect";
import GameScreen from "../components/GameScreen";
import { apiClient } from "../lib/api";

export default function Home() {
  const { isAuthenticated, token, currentView, syncWithServer } =
    useGameStore();

  useEffect(() => {
    // Set token in API client if user is authenticated
    if (token) {
      apiClient.setToken(token);
    }
  }, [token]);

  useEffect(() => {
    // Sync with server periodically if authenticated
    if (isAuthenticated) {
      const interval = setInterval(syncWithServer, 30000); // Every 30 seconds
      return () => clearInterval(interval);
    }
  }, [isAuthenticated, syncWithServer]);

  if (!isAuthenticated) {
    return <AuthScreen />;
  }

  switch (currentView) {
    case "character-select":
      return <CharacterSelect />;
    case "dungeon":
    case "combat":
    case "inventory":
      return <GameScreen />;
    default:
      return <CharacterSelect />;
  }
}
