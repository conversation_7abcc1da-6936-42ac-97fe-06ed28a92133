import { NextRequest, NextResponse } from 'next/server';

// Mock user storage (in production, this would be in a database)
const mockUsers: any[] = [];

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, username, password } = body;

    // Basic validation
    if (!email || !username || !password) {
      return NextResponse.json({
        success: false,
        error: 'Email, username, and password are required'
      }, { status: 400 });
    }

    // Check if user already exists
    const existingUser = mockUsers.find(u => u.email === email || u.username === username);
    if (existingUser) {
      return NextResponse.json({
        success: false,
        error: 'User with this email or username already exists'
      }, { status: 400 });
    }

    // Create mock user
    const newUser = {
      id: `user_${Date.now()}`,
      email,
      username,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_active: true
    };

    mockUsers.push({ ...newUser, password });

    return NextResponse.json({
      success: true,
      data: newUser,
      message: 'User created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Registration error:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Registration failed'
    }, { status: 400 });
  }
}
