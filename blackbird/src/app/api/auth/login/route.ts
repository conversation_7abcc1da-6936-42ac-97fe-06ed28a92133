import { NextRequest, NextResponse } from 'next/server';

// Mock user storage (shared with register)
const mockUsers: any[] = [
  {
    id: 'demo_user',
    email: '<EMAIL>',
    username: 'demo',
    password: 'demo123',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    is_active: true
  }
];

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    // Basic validation
    if (!email || !password) {
      return NextResponse.json({
        success: false,
        error: 'Email and password are required'
      }, { status: 400 });
    }

    // Find user
    const user = mockUsers.find(u => u.email === email && u.password === password);
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Invalid email or password'
      }, { status: 401 });
    }

    // Generate mock token
    const token = `mock_token_${user.id}_${Date.now()}`;

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user;

    return NextResponse.json({
      success: true,
      data: { user: userWithoutPassword, token },
      message: 'Login successful'
    });

  } catch (error) {
    console.error('Login error:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Login failed'
    }, { status: 401 });
  }
}
