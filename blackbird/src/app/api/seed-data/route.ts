import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Mock data seeding
    console.log('Mock: Database seeded');
    
    return NextResponse.json({
      success: true,
      message: 'Database seeded successfully (mock)'
    });
  } catch (error) {
    console.error('Database seeding error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to seed database',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
