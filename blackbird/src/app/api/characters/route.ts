import { NextRequest, NextResponse } from 'next/server';

// Mock character storage
const mockCharacters: any[] = [];

// Mock inventory items
const mockItems = [
  {
    id: 'item_1',
    name: 'Rusty Sword',
    type: 'weapon',
    rarity: 'common',
    attack_bonus: 5,
    defense_bonus: 0,
    health_bonus: 0,
    speed_bonus: 0,
    heal_amount: 0,
    description: 'A worn but functional sword.',
    price: 10
  },
  {
    id: 'item_2',
    name: 'Leather Vest',
    type: 'armor',
    rarity: 'common',
    attack_bonus: 0,
    defense_bonus: 3,
    health_bonus: 5,
    speed_bonus: 0,
    heal_amount: 0,
    description: 'Basic leather protection.',
    price: 15
  },
  {
    id: 'item_3',
    name: 'Health Potion',
    type: 'consumable',
    rarity: 'common',
    attack_bonus: 0,
    defense_bonus: 0,
    health_bonus: 0,
    speed_bonus: 0,
    heal_amount: 30,
    description: 'Restores 30 health points.',
    price: 20
  }
];

function authenticateRequest(request: NextRequest) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  
  const token = authHeader.substring(7);
  // Mock authentication - in real app, verify JWT
  if (token.startsWith('mock_token_')) {
    return { id: 'demo_user', email: '<EMAIL>' };
  }
  
  return null;
}

export async function GET(request: NextRequest) {
  try {
    const user = authenticateRequest(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 });
    }

    // Get user's characters
    const userCharacters = mockCharacters.filter(c => c.user_id === user.id);

    return NextResponse.json({
      success: true,
      data: userCharacters
    });

  } catch (error) {
    console.error('Characters API error:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = authenticateRequest(request);
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 });
    }

    const body = await request.json();
    const { name } = body;

    if (!name || name.trim().length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Character name is required'
      }, { status: 400 });
    }

    // Check if character name already exists for this user
    const existingCharacter = mockCharacters.find(c => c.user_id === user.id && c.name === name);
    if (existingCharacter) {
      return NextResponse.json({
        success: false,
        error: 'Character name already exists'
      }, { status: 400 });
    }

    // Create new character
    const newCharacter = {
      id: `char_${Date.now()}`,
      user_id: user.id,
      name: name.trim(),
      level: 1,
      experience: 0,
      health: 100,
      max_health: 100,
      attack: 10,
      defense: 5,
      speed: 10,
      gold: 50,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    mockCharacters.push(newCharacter);

    return NextResponse.json({
      success: true,
      data: newCharacter,
      message: 'Character created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Characters API error:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error'
    }, { status: 500 });
  }
}
